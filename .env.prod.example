# Firespoon API Production Environment Configuration
# Copy this file to .env.prod and fill in your production values
# IMPORTANT: Never commit this file with real credentials to version control

# Node environment
NODE_ENV=production

# Server Configuration
SERVER_URL=https://your-production-domain.com
PORT=8000

# JWT Secret for authentication (REQUIRED)
# Generate a strong secret key for JWT token signing
# Example: openssl rand -base64 32
# CRITICAL: Use a different secret than development/test environments
JWT_SECRET=your-production-jwt-secret-here

# Database Configuration
CONNECTION_STRING=mongodb+srv://username:<EMAIL>/firespoon_prod
REDIS_URL=rediss://username:password@your-redis-host:port
REDIS_ENABLE_TLS=true

# Firebase Configuration (REQUIRED)
# Set this to the entire JSON content of your production service account key
FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-prod-project",...}'

# WhatsApp API Configuration
WS_API_URL=https://your-production-whatsapp-api.com
WS_AUTH_URL=https://your-production-auth-endpoint.com
WS_BOUNDARY=your-production-boundary-string

# Payemoji credentials (Production)
PAYEMOJI_CLIENT_ID=your-production-client-id
PAYEMOJI_CLIENT_SECRET=your-production-client-secret
PAYEMOJI_WEBHOOK_SECRET=your-production-webhook-secret
PAYEMOJI_AGENT_ID=your-production-agent-id

# Stripe API keys (Production)
STRIPE_SECRET_KEY=sk_live_your_live_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
STRIPE_CHECKOUT_BASE_URL=https://your-production-frontend.com

# WhatsApp Service Configuration
WS_GRANT_TYPE=client_credentials
WS_SCOPE=your-production-scope
WS_B2C_POLICY=your-production-policy

# Service URLs
ORDER_MANAGEMENT_URL=https://your-production-order-management.com
ADDRESS_MANAGEMENT_URL=https://your-production-address-management.com

# Template IDs
WS_TEMPLATE_BASIC_TEXT=your-production-template-id

# Dialog Configuration
DIALOG_SESSION_TOKEN_BYTES=32
DIALOG_SESSION_TTL_SECONDS=3600

# Logging
LOG_LEVEL=info
CONSOLE_LOG_LEVEL=warn
FILE_LOG_LEVEL=info

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://your-production-frontend.com
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Currency Configuration
DEFAULT_CURRENCY=USD
DEFAULT_CURRENCY_SYMBOL=$
