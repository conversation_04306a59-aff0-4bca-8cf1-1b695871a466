{"summary": {"totalTests": 52, "passedTests": 24, "failedTests": 0, "skippedTests": 28, "totalDuration": 14600, "success": false, "timestamp": "2025-06-21T11:08:54.925Z", "environment": "test"}, "projects": {"INTEGRATION": {"tests": [{"title": "should process incoming message and create session", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["WhatsApp Webhook Integration"]}, {"title": "should reject webhook with invalid signature", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["WhatsApp Webhook Integration"]}, {"title": "should handle malformed webhook payload", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["WhatsApp Webhook Integration"]}, {"title": "should verify refundStatus default value is NONE", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"]}, {"title": "should verify refund status enum values", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"]}, {"title": "should create real payment intent with <PERSON>e", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"]}, {"title": "should retrieve real payment intent status", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"]}, {"title": "should handle payment with test card", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"]}, {"title": "should handle declined card", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"]}, {"title": "should create real Stripe customer", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"]}, {"title": "should retrieve real Stripe customer", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"]}, {"title": "should process real webhook signature validation", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Webhook Processing"]}, {"title": "should handle invalid amount", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"]}, {"title": "should handle invalid currency", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"]}, {"title": "should create a new order", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL Mutations"]}, {"title": "should fail to create order with invalid restaurant ID", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL Mutations"]}, {"title": "should fail to create order without authentication", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL Mutations"]}, {"title": "should update order status", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL Mutations"]}, {"title": "should have Order type in schema", "status": "passed", "duration": 303, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"]}, {"title": "should have OrderStatus enum in schema", "status": "passed", "duration": 32, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"]}, {"title": "should have OrderInput type in schema", "status": "passed", "duration": 29, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"]}, {"title": "should validate GraphQL order operations", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"]}, {"title": "should handle malformed order queries", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"]}, {"title": "should validate required arguments", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"]}, {"title": "should respond to GraphQL introspection query", "status": "passed", "duration": 63, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"]}, {"title": "should have Restaurant type in schema", "status": "passed", "duration": 28, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"]}, {"title": "should have payment related types in schema", "status": "passed", "duration": 55, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"]}, {"title": "should have query types in schema", "status": "passed", "duration": 27, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"]}, {"title": "should have payment related types in schema", "status": "passed", "duration": 84, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"]}, {"title": "should have mutation types in schema", "status": "passed", "duration": 48, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"]}, {"title": "should have Customer type in schema", "status": "passed", "duration": 61, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"]}, {"title": "should have Address type in schema", "status": "passed", "duration": 34, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"]}, {"title": "should have AddressInput type in schema", "status": "passed", "duration": 35, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Input Types"]}, {"title": "should validate customer-related mutations exist", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"]}, {"title": "should handle GraphQL validation errors", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"]}, {"title": "should have order query in schema", "status": "passed", "duration": 57, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"]}, {"title": "should have orders query in schema", "status": "passed", "duration": 27, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"]}, {"title": "should have updateOrderStatus mutation in schema", "status": "passed", "duration": 59, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"]}, {"title": "should have Order type with orderStatus field in schema", "status": "passed", "duration": 24, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"]}, {"title": "should have order status enum values in schema", "status": "passed", "duration": 25, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"]}, {"title": "should respond to GraphQL endpoint", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"]}, {"title": "should handle invalid GraphQL queries", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"]}, {"title": "should have Restaurant type in schema", "status": "passed", "duration": 56, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Restaurant Schema"]}, {"title": "should handle simple queries", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"]}, {"title": "should validate GraphQL syntax", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"]}, {"title": "should handle empty queries", "status": "pending", "duration": null, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"]}, {"title": "should support schema introspection", "status": "passed", "duration": 25, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"]}, {"title": "should list available queries", "status": "passed", "duration": 24, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"]}, {"title": "should list available mutations", "status": "passed", "duration": 27, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"]}, {"title": "should have order subscription in schema", "status": "passed", "duration": 55, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"]}, {"title": "should have mutation types in schema", "status": "passed", "duration": 25, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"]}, {"title": "should have payment related types in schema", "status": "passed", "duration": 60, "failureMessages": [], "ancestorTitles": ["Payment System Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"]}], "summary": {"total": 52, "passed": 24, "failed": 0, "skipped": 28, "duration": 1263}}}, "coverage": {}, "performance": {}}