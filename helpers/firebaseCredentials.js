const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('./logger');

/**
 * 获取Firebase服务账户凭据
 * 优先使用环境变量，在开发环境中可以fallback到本地文件
 * @returns {Object} Firebase服务账户凭据对象
 */
function getFirebaseCredentials() {
  // 首先尝试从环境变量获取
  if (config.FIREBASE.SERVICE_ACCOUNT_KEY) {
    try {
      const credentials = JSON.parse(config.FIREBASE.SERVICE_ACCOUNT_KEY);
      logger.info('Using Firebase credentials from environment variable');
      return credentials;
    } catch (error) {
      logger.error('Failed to parse FIREBASE_SERVICE_ACCOUNT_KEY environment variable:', error);
      throw new Error('Invalid FIREBASE_SERVICE_ACCOUNT_KEY format. Must be valid JSON.');
    }
  }

  // 在生产环境中，环境变量是必需的
  if (config.NODE_ENV === 'production' || config.NODE_ENV === 'render') {
    throw new Error('FIREBASE_SERVICE_ACCOUNT_KEY environment variable is required in production environment');
  }

  // 在开发/测试环境中，尝试从本地文件读取（向后兼容）
  const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');
  
  if (fs.existsSync(serviceAccountPath)) {
    try {
      const credentials = require(serviceAccountPath);
      logger.warn('Using Firebase credentials from local file. Consider migrating to environment variables for better security.');
      logger.warn('Set FIREBASE_SERVICE_ACCOUNT_KEY environment variable to avoid this warning.');
      return credentials;
    } catch (error) {
      logger.error('Failed to read serviceAccountKey.json:', error);
      throw new Error('Failed to load Firebase credentials from local file');
    }
  }

  // 如果既没有环境变量也没有本地文件
  throw new Error(
    'Firebase credentials not found. Please either:\n' +
    '1. Set FIREBASE_SERVICE_ACCOUNT_KEY environment variable with the JSON content, or\n' +
    '2. Place serviceAccountKey.json file in the project root (development only)'
  );
}

/**
 * 验证Firebase凭据格式
 * @param {Object} credentials - Firebase凭据对象
 * @returns {boolean} 是否有效
 */
function validateFirebaseCredentials(credentials) {
  const requiredFields = [
    'type',
    'project_id',
    'private_key_id',
    'private_key',
    'client_email',
    'client_id',
    'auth_uri',
    'token_uri'
  ];

  for (const field of requiredFields) {
    if (!credentials[field]) {
      logger.error(`Missing required Firebase credential field: ${field}`);
      return false;
    }
  }

  if (credentials.type !== 'service_account') {
    logger.error('Invalid Firebase credential type. Expected "service_account"');
    return false;
  }

  return true;
}

/**
 * 获取并验证Firebase凭据
 * @returns {Object} 验证过的Firebase凭据对象
 */
function getValidatedFirebaseCredentials() {
  const credentials = getFirebaseCredentials();
  
  if (!validateFirebaseCredentials(credentials)) {
    throw new Error('Invalid Firebase credentials format');
  }

  return credentials;
}

module.exports = {
  getFirebaseCredentials,
  validateFirebaseCredentials,
  getValidatedFirebaseCredentials
};
