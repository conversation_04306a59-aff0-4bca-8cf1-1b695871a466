var admin = require('firebase-admin');
const { getValidatedFirebaseCredentials } = require('./firebaseCredentials');
const logger = require('./logger');

// 获取Firebase凭据
const serviceAccount = getValidatedFirebaseCredentials();

const firebaseWebApp = admin.initializeApp(
  {
    credential: admin.credential.cert(serviceAccount)
  },
  'firebase-web-notifications'
);

function sendNotificationToCustomerWeb(token, title, body) {
  if (!token) {
    logger.warn('Web notification skipped - no token provided', {
      title,
      body
    });
    return;
  }

  const message = {
    notification: {
      title,
      body
    },
    token
  };

  admin
    .messaging(firebaseWebApp)
    .send(message)
    .then(response => {
      logger.info('Successfully sent web notification', {
        messageId: response,
        title,
        token: token.substring(0, 6) + '...' // Only log part of the token for security
      });
    })
    .catch(error => {
      logger.error('Failed to send web notification', {
        error: error.message,
        errorCode: error.code,
        title,
        token: token.substring(0, 6) + '...' // Only log part of the token for security
      });
    });
}
module.exports.sendNotificationToCustomerWeb = sendNotificationToCustomerWeb;
