/**
 * E2E测试专用setup配置
 * 模拟真实用户场景，无Mock
 */

const { createTestApp } = require('../helpers/testApp');
const { connectTestDB, clearTestDB } = require('../helpers/testDatabase');

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';

let testApp;

// E2E测试全局设置
beforeAll(async () => {
  console.log('🚀 Setting up E2E test environment...');
  
  // 连接测试数据库
  await connectTestDB({ useRealDatabase: true });
  
  // 创建测试应用
  testApp = await createTestApp();
  global.testApp = testApp;
  global.baseURL = `http://localhost:${testApp.port}`;
  
  console.log(`✅ E2E test server started on port ${testApp.port}`);
});

beforeEach(async () => {
  // 每个测试前清理数据库
  await clearTestDB();
});

afterAll(async () => {
  console.log('🧹 Cleaning up E2E test environment...');
  
  if (testApp) {
    await testApp.close();
  }
  
  console.log('✅ E2E cleanup completed');
});

// 设置Jest超时
jest.setTimeout(60000);

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in E2E Test:', promise, 'reason:', reason);
});

// 保持console输出用于调试
global.console = console;
