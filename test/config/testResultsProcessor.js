/**
 * 测试结果处理器
 * 处理和格式化Jest测试结果，符合SOP要求
 */

const fs = require('fs');
const path = require('path');
const LogCompactor = require('../scripts/compactLogs');

function findLatestTimestampDir(reportsDir) {
  try {
    if (!fs.existsSync(reportsDir)) {
      return null;
    }

    const entries = fs.readdirSync(reportsDir, { withFileTypes: true });
    const timestampDirs = entries
      .filter(entry => entry.isDirectory() && entry.name.startsWith('test_'))
      .map(entry => {
        const fullPath = path.join(reportsDir, entry.name);
        const stats = fs.statSync(fullPath);
        return {
          path: fullPath,
          name: entry.name,
          mtime: stats.mtime
        };
      })
      .sort((a, b) => b.mtime - a.mtime); // 按修改时间降序排列

    return timestampDirs.length > 0 ? timestampDirs[0].path : null;
  } catch (error) {
    console.warn('查找时间戳目录失败:', error.message);
    return null;
  }
}

function processTestResults(testResults) {
  const processedResults = {
    ...testResults,
    processedAt: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      env: process.env.NODE_ENV
    },
    summary: generateSummary(testResults),
    recommendations: generateRecommendations(testResults)
  };

  // 保存处理后的结果
  saveProcessedResults(processedResults);
  
  // 生成测试报告
  generateTestReport(processedResults);

  // 压缩日志文件
  compactLogFiles();

  return processedResults;
}

function generateSummary(testResults) {
  const summary = {
    total: testResults.numTotalTests,
    passed: testResults.numPassedTests,
    failed: testResults.numFailedTests,
    skipped: testResults.numPendingTests,
    duration: testResults.testResults.reduce((total, result) => {
      return total + (result.perfStats?.end - result.perfStats?.start || 0);
    }, 0),
    successRate: testResults.numTotalTests > 0 
      ? (testResults.numPassedTests / testResults.numTotalTests * 100).toFixed(2)
      : 0
  };

  // 按测试类型分组
  summary.byType = {};
  testResults.testResults.forEach(result => {
    const displayName = result.displayName?.name || 'Unknown';
    if (!summary.byType[displayName]) {
      summary.byType[displayName] = {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
      };
    }
    
    const typeStats = summary.byType[displayName];
    result.testResults.forEach(test => {
      typeStats.total++;
      typeStats.duration += test.duration || 0;
      
      switch (test.status) {
        case 'passed':
          typeStats.passed++;
          break;
        case 'failed':
          typeStats.failed++;
          break;
        default:
          typeStats.skipped++;
      }
    });
  });

  return summary;
}

function generateRecommendations(testResults) {
  const recommendations = [];
  const summary = generateSummary(testResults);

  // 成功率建议
  if (summary.successRate < 90) {
    recommendations.push({
      type: 'quality',
      priority: 'high',
      message: `测试成功率 ${summary.successRate}% 低于推荐的90%，需要修复失败的测试`
    });
  }

  // 性能建议
  const avgDuration = summary.duration / summary.total;
  if (avgDuration > 1000) {
    recommendations.push({
      type: 'performance',
      priority: 'medium',
      message: `平均测试时间 ${avgDuration.toFixed(2)}ms 较长，考虑优化测试性能`
    });
  }

  // 覆盖率建议
  if (testResults.coverageMap) {
    const coverageSummary = testResults.coverageMap.getCoverageSummary();
    if (coverageSummary.statements.pct < 75) {
      recommendations.push({
        type: 'coverage',
        priority: 'medium',
        message: `代码覆盖率 ${coverageSummary.statements.pct}% 低于推荐的75%`
      });
    }
  }

  // 测试分布建议
  Object.entries(summary.byType).forEach(([type, stats]) => {
    if (type === 'UNIT' && stats.total < summary.total * 0.7) {
      recommendations.push({
        type: 'structure',
        priority: 'low',
        message: '单元测试数量偏少，建议增加单元测试以符合测试金字塔原则'
      });
    }
  });

  return recommendations;
}

function saveProcessedResults(results) {
  try {
    const reportDir = path.join(__dirname, '../reports');

    // 查找最新的时间戳目录
    const timestampDir = findLatestTimestampDir(reportDir);

    // 保存到时间戳目录或根目录，文件名包含时间戳
    let outputFile;
    if (timestampDir) {
      const dirName = path.basename(timestampDir);
      const timestamp = dirName.replace('test_', '');
      outputFile = path.join(timestampDir, `processed-results_${timestamp}.json`);
    } else {
      outputFile = path.join(reportDir, 'processed-results.json');
    }

    if (!fs.existsSync(path.dirname(outputFile))) {
      fs.mkdirSync(path.dirname(outputFile), { recursive: true });
    }

    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));

    console.log(`📊 处理后的测试结果已保存: ${outputFile}`);

    // 移动其他报告文件到时间戳目录
    if (timestampDir) {
      moveReportsToTimestampDir(reportDir, timestampDir);
    }
  } catch (error) {
    console.error('❌ 保存处理结果失败:', error.message);
  }
}

function generateTestReport(results) {
  try {
    const reportDir = path.join(__dirname, '../reports');

    // 查找最新的时间戳目录
    const timestampDir = findLatestTimestampDir(reportDir);
    let reportFile;
    if (timestampDir) {
      const dirName = path.basename(timestampDir);
      const timestamp = dirName.replace('test_', '');
      reportFile = path.join(timestampDir, `test-report_${timestamp}.md`);
    } else {
      reportFile = path.join(reportDir, 'test-report.md'); // 回退到根目录
    }
    
    const report = `# Firespoon API 测试报告

## 📊 测试概览

- **测试时间**: ${results.processedAt}
- **环境**: ${results.environment.env}
- **Node版本**: ${results.environment.nodeVersion}
- **平台**: ${results.environment.platform} ${results.environment.arch}

## 📈 测试结果

| 指标 | 数值 |
|------|------|
| 总测试数 | ${results.summary.total} |
| 通过 | ${results.summary.passed} |
| 失败 | ${results.summary.failed} |
| 跳过 | ${results.summary.skipped} |
| 成功率 | ${results.summary.successRate}% |
| 总耗时 | ${(results.summary.duration / 1000).toFixed(2)}s |

## 🔍 分类测试结果

${Object.entries(results.summary.byType).map(([type, stats]) => `
### ${type} 测试
- 总数: ${stats.total}
- 通过: ${stats.passed}
- 失败: ${stats.failed}
- 跳过: ${stats.skipped}
- 成功率: ${stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(2) : 0}%
- 耗时: ${(stats.duration / 1000).toFixed(2)}s
`).join('')}

## 💡 改进建议

${results.recommendations.length > 0 
  ? results.recommendations.map(rec => `
### ${rec.type.toUpperCase()} - ${rec.priority.toUpperCase()}
${rec.message}
`).join('')
  : '✅ 暂无改进建议，测试质量良好！'
}

## 📋 详细信息

完整的测试结果数据请查看: \`processed-results_[timestamp].json\` (同目录下)

---
*报告生成时间: ${new Date().toISOString()}*
`;

    fs.writeFileSync(reportFile, report);
    console.log(`📄 测试报告已生成: ${reportFile}`);
  } catch (error) {
    console.error('❌ 生成测试报告失败:', error.message);
  }
}

function moveReportsToTimestampDir(reportDir, timestampDir) {
  try {
    // 从目录名提取时间戳
    const dirName = path.basename(timestampDir);
    const timestamp = dirName.replace('test_', '');

    const filesToMove = [
      { source: 'test-report.html', target: `test-report_${timestamp}.html` },
      { source: 'test-results.xml', target: `test-results_${timestamp}.xml` }
    ];

    filesToMove.forEach(({ source, target }) => {
      const sourcePath = path.join(reportDir, source);
      const targetPath = path.join(timestampDir, target);

      if (fs.existsSync(sourcePath)) {
        fs.renameSync(sourcePath, targetPath);
        console.log(`📁 已移动 ${source} -> ${target}`);
      }
    });

    // 移动jest-html-reporters-attach目录到时间戳目录
    const attachSourceDir = path.join(reportDir, 'jest-html-reporters-attach');
    const attachTargetDir = path.join(timestampDir, 'jest-html-reporters-attach');

    if (fs.existsSync(attachSourceDir)) {
      // 使用递归复制然后删除原目录
      copyDirectoryRecursive(attachSourceDir, attachTargetDir);
      removeDirectoryRecursive(attachSourceDir);
      console.log(`📁 已移动 jest-html-reporters-attach 目录到时间戳目录`);
    }
  } catch (error) {
    console.warn('⚠️ 移动报告文件时出现问题:', error.message);
  }
}

function copyDirectoryRecursive(source, target) {
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  const files = fs.readdirSync(source);
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    if (fs.statSync(sourcePath).isDirectory()) {
      copyDirectoryRecursive(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

function removeDirectoryRecursive(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
  }
}

function compactLogFiles() {
  try {
    console.log('🗜️  开始压缩日志文件...');
    const reportDir = path.join(__dirname, '../reports');
    const compactor = new LogCompactor();
    compactor.processAllLogFiles(reportDir);
    console.log('✅ 日志压缩完成');
  } catch (error) {
    console.warn('⚠️ 日志压缩失败:', error.message);
  }
}

module.exports = processTestResults;
