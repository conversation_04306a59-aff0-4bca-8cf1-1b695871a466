/**
 * 集成测试专用setup配置
 * 使用真实数据库，最小化Mock
 */

const { clearTestDB } = require('../helpers/testDatabase');

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';

// 集成测试前清理数据库
beforeEach(async () => {
  try {
    await clearTestDB();
  } catch (error) {
    console.warn('Failed to clear test database:', error.message);
  }
});

// 设置Jest超时
jest.setTimeout(30000);

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in Integration Test:', promise, 'reason:', reason);
});

// 保持console输出用于调试
global.console = console;
