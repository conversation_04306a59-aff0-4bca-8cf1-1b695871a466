/**
 * 详细日志报告器包装器
 * 符合SOP 2.2 Logging Rules要求
 */

const DetailedLogger = require('./detailedLogger');

class DetailedLogReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options;
    this.logger = new DetailedLogger();
  }

  onRunStart(results, options) {
    this.logger.onRunStart(results, options);
  }

  onTestFileStart(test) {
    this.logger.onTestFileStart(test);
  }

  onTestCaseStart(test, testCaseResult) {
    this.logger.onTestCaseStart(test, testCaseResult);
  }

  onTestCaseResult(test, testCaseResult) {
    this.logger.onTestCaseResult(test, testCaseResult);
  }

  onTestFileResult(test, testResult) {
    this.logger.onTestFileResult(test, testResult);
  }

  onRunComplete(contexts, results) {
    this.logger.onRunComplete(contexts, results);
  }
}

module.exports = DetailedLogReporter;
