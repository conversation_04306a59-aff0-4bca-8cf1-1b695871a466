/**
 * 自定义Jest报告器
 * 符合SOP要求的测试结果输出格式
 */

const fs = require('fs');
const path = require('path');
const ActLogCapture = require('./actLogCapture');

// 全局日志捕获器
let globalLogCapture = null;

class CustomReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options;

    // 创建时间戳文件夹 (精确到秒)
    this.runTimestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
    this.runDir = path.join(__dirname, '../reports', `test_${this.runTimestamp}`);

    // 确保报告目录存在
    if (!fs.existsSync(this.runDir)) {
      fs.mkdirSync(this.runDir, { recursive: true });
    }

    this.outputFile = path.join(this.runDir, `test-summary_${this.runTimestamp}.json`);
    this.rawLogFile = path.join(this.runDir, `test-run_${this.runTimestamp}_raw.log`);
    this.cleanLogFile = path.join(this.runDir, `test-run_${this.runTimestamp}_clean.log`);

    this.startTime = Date.now();
    this.testResults = {
      summary: {},
      projects: {},
      coverage: {},
      performance: {}
    };

    // 存储每个测试的日志捕获器
    this.testLogCaptures = new Map();
  }

  onRunStart(results, options) {
    console.log('\n🚀 Firespoon API 测试框架启动');
    console.log('='.repeat(80));
    console.log(`📁 测试报告目录: ${this.runDir}`);
    this.startTime = Date.now();

    // 启动全局日志捕获
    if (!globalLogCapture) {
      globalLogCapture = new ActLogCapture();
      globalLogCapture.startCapture();
      console.log('📝 日志捕获器已启动');
    }

    // 初始化详细日志文件
    this._writeLogFile('================================================================================');
    this._writeLogFile('Test Run Started');
    this._writeLogFile(`Timestamp: ${new Date().toISOString()}`);
    this._writeLogFile(`Operating System: ${process.platform} ${process.arch}`);
    this._writeLogFile(`Runtime Version: Node.js ${process.version}`);
    this._writeLogFile(`Working Directory: ${process.cwd()}`);
    this._writeLogFile('================================================================================');
  }

  onTestResult(test, testResult, aggregatedResult) {
    const projectName = testResult.displayName?.name || 'Unknown';

    if (!this.testResults.projects[projectName]) {
      this.testResults.projects[projectName] = {
        tests: [],
        summary: {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0,
          duration: 0
        }
      };
    }

    const project = this.testResults.projects[projectName];

    // 记录测试套件开始
    this._writeLogFile(`\n[SUITE START] - File: ${test.path}`);

    testResult.testResults.forEach((result, index) => {
      const caseId = this._generateCaseId(projectName, result, index);
      const status = this._mapJestStatusToSOP(result.status);
      const duration = result.duration || 0;

      // 输出SOP格式的控制台结果
      this._printSOPTestResult(caseId, result, status);

      // 获取测试期间捕获的日志（如果有的话）
      const capturedLogs = this._getCapturedLogsForTest(caseId) || [];

      // 记录详细日志
      this._writeDetailedTestLog(caseId, result, status, duration, test.path, capturedLogs);

      project.tests.push({
        caseId,
        title: result.title,
        status: result.status,
        duration: duration,
        failureMessages: result.failureMessages,
        ancestorTitles: result.ancestorTitles,
        capturedLogs: capturedLogs.length
      });

      project.summary.total++;
      project.summary.duration += duration;

      switch (result.status) {
        case 'passed':
          project.summary.passed++;
          break;
        case 'failed':
          project.summary.failed++;
          break;
        case 'skipped':
        case 'pending':
          project.summary.skipped++;
          break;
      }
    });

    this._writeLogFile(`[SUITE END] - File: ${test.path}`);
  }

  onRunComplete(contexts, results) {
    const duration = Date.now() - this.startTime;

    // 生成总结报告
    this.testResults.summary = {
      totalTests: results.numTotalTests,
      passedTests: results.numPassedTests,
      failedTests: results.numFailedTests,
      skippedTests: results.numPendingTests,
      totalDuration: duration,
      success: results.success,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown'
    };

    // 覆盖率信息
    if (results.coverageMap) {
      this.testResults.coverage = {
        statements: results.coverageMap.getCoverageSummary().statements,
        branches: results.coverageMap.getCoverageSummary().branches,
        functions: results.coverageMap.getCoverageSummary().functions,
        lines: results.coverageMap.getCoverageSummary().lines
      };
    }

    // 完成详细日志
    this._writeLogFile('\n================================================================================');
    this._writeLogFile('Test Run Finished');
    this._writeLogFile(`End Timestamp: ${new Date().toISOString()}`);
    this._writeLogFile(`Total Duration: ${duration}ms (${(duration / 1000).toFixed(2)}s)`);
    this._writeLogFile(`Total Tests: ${results.numTotalTests}`);
    this._writeLogFile(`Passed Tests: ${results.numPassedTests}`);
    this._writeLogFile(`Failed Tests: ${results.numFailedTests}`);
    this._writeLogFile(`Skipped Tests: ${results.numPendingTests}`);
    this._writeLogFile(`Success Rate: ${((results.numPassedTests / results.numTotalTests) * 100).toFixed(2)}%`);
    this._writeLogFile(`Overall Result: ${results.success ? 'SUCCESS' : 'FAILURE'}`);
    this._writeLogFile('================================================================================');

    // 输出SOP格式的控制台总结
    this._printSOPSummary(results);

    // 停止日志捕获
    if (globalLogCapture) {
      const finalLogs = globalLogCapture.stopCapture();
      console.log(`📝 日志捕获器已停止，共捕获 ${finalLogs.length} 条日志`);
      globalLogCapture = null;
    }

    // 保存JSON报告
    this._saveJsonReport();
  }





  _saveJsonReport() {
    try {
      fs.writeFileSync(this.outputFile, JSON.stringify(this.testResults, null, 2));
      console.log(`\n📄 详细报告已保存: ${this.outputFile}`);
    } catch (error) {
      console.error('❌ 保存测试报告失败:', error.message);
    }
  }

  // SOP格式输出方法
  _printSOPTestResult(caseId, result, status) {
    const description = result.title;

    if (status === 'PASS') {
      console.log(`[PASS] - ${caseId}: ${description}`);
      console.log(`├─ Expected: Test should complete successfully`);
      console.log(`└─ Actual: Test completed without errors`);
    } else if (status === 'FAIL') {
      const errorMsg = result.failureMessages?.[0]?.split('\n')[0] || 'Test failed';
      console.log(`[FAIL] - ${caseId}: ${description}`);
      console.log(`├─ Expected: Test should pass`);
      console.log(`└─ Actual: ${errorMsg}`);
    } else if (status === 'ERROR') {
      console.log(`[ERROR] - ${caseId}: ${description}`);
      console.log(`├─ Description: An unexpected error occurred during test execution.`);
      console.log(`└─ Exception: ${result.failureMessages?.[0] || 'Unknown error'}`);
    } else if (status === 'SKIP') {
      console.log(`[SKIP] - ${caseId}: ${description}`);
      console.log(`└─ Reason: Test marked as pending/skipped`);
    }
  }

  _printSOPSummary(results) {
    console.log('\n' + '='.repeat(80));
    console.log('================== Test Run Summary ==================');
    console.log(`Total Tests: ${results.numTotalTests}`);
    console.log(`Passed:      ${results.numPassedTests}`);
    console.log(`Failed:      ${results.numFailedTests}`);
    console.log(`Errors:      0`); // Jest doesn't distinguish errors from failures
    console.log(`Skipped:     ${results.numPendingTests}`);
    console.log('');
    console.log(`For detailed logs, see:`);
    console.log(`  Raw (with colors): ${this.rawLogFile}`);
    console.log(`  Clean (no colors): ${this.cleanLogFile}`);
    console.log('='.repeat(56));
  }

  _mapJestStatusToSOP(jestStatus) {
    switch (jestStatus) {
      case 'passed': return 'PASS';
      case 'failed': return 'FAIL';
      case 'pending':
      case 'skipped': return 'SKIP';
      default: return 'ERROR';
    }
  }

  _generateCaseId(projectName, result, index) {
    const prefixes = {
      'UNIT': 'U',
      'INTEGRATION': 'I',
      'E2E': 'E',
      'PERFORMANCE': 'P'
    };
    const prefix = prefixes[projectName] || 'T';
    return `${prefix}-AUTO-${String(index + 1).padStart(3, '0')}`;
  }

  _writeDetailedTestLog(caseId, result, status, duration, testPath, capturedLogs = []) {
    this._writeLogFile(`\n[CASE START] - ${caseId}: ${result.title}`);
    this._writeLogFile(`Module: ${this._extractModule(result.ancestorTitles)}`);
    this._writeLogFile(`Full Path: ${result.ancestorTitles.join(' › ')} › ${result.title}`);

    if (status === 'SKIP') {
      this._writeLogFile(`[RESULT] - SKIP: Test marked as pending/skipped`);
    } else {
      this._writeLogFile(`[Arrange] - Precondition: Setting up test environment`);
      this._writeLogFile(`[Act] - Step: Executing test logic`);

      // 记录捕获的程序输出
      if (capturedLogs.length > 0) {
        this._writeLogFile(`[Act Log] - Captured ${capturedLogs.length} program outputs during execution`);
        this._writeLogFile(`[Act Log] - === Program Output Details ===`);
        capturedLogs.forEach((log, index) => {
          const timeStr = `+${log.timestamp || 0}ms`;
          if (log.error) {
            this._writeLogFile(`[Act Log] - ${timeStr} [${log.type}] ERROR: ${log.message}`);
            if (log.error.stack) {
              const stackLines = log.error.stack.split('\n').slice(0, 3);
              stackLines.forEach(line => {
                this._writeLogFile(`[Act Log] -   ${line.trim()}`);
              });
            }
          } else {
            this._writeLogFile(`[Act Log] - ${timeStr} [${log.type}] ${log.message}`);
          }
        });
        this._writeLogFile(`[Act Log] - === End Program Output ===`);
      } else {
        this._writeLogFile(`[Act Log] - No program outputs captured (silent execution)`);
      }

      if (status === 'PASS') {
        this._writeLogFile(`[Assert] - Verifying: All assertions passed`);

        // 获取enhanced-expect的断言日志
        const assertionLogs = global.assertionLogs || [];
        if (assertionLogs.length > 0) {
          assertionLogs.forEach(assertion => {
            this._writeLogFile(`[Assert Log] - Expected: ${assertion.expected}`);
            this._writeLogFile(`[Assert Log] - Actual: ${assertion.actual}`);
            this._writeLogFile(`[Assert Log] - Comparison: expect().${assertion.matcher}() - ${assertion.result}`);
            if (assertion.message) {
              this._writeLogFile(`[Assert Log] - Details: ${assertion.message}`);
            }
          });
        } else {
          this._writeLogFile(`[Assert Log] - Expected: Test completion without errors`);
          this._writeLogFile(`[Assert Log] - Actual: Test completed successfully`);
        }

        this._writeLogFile(`[RESULT] - PASS: Test completed successfully`);
      } else {
        const errorMsg = result.failureMessages?.[0] || 'Unknown error';
        this._writeLogFile(`[Assert] - Verifying: Test assertions failed`);
        this._writeLogFile(`[Assert Log] - Expected: Test should pass`);
        this._writeLogFile(`[Assert Log] - Actual: ${errorMsg.split('\n')[0]}`);
        this._writeLogFile(`[RESULT] - FAIL: ${errorMsg.split('\n')[0]}`);
      }
    }

    this._writeLogFile(`[CASE END] - Duration: ${duration}ms`);
  }

  _writeLogFile(message) {
    const timestamp = new Date().toISOString();

    // 原始日志条目（保留ANSI码）
    const rawLogEntry = `[${timestamp}] ${message}`;

    // 清理后的日志条目（移除ANSI码）
    const cleanMessage = this._stripAnsiCodes(message);
    const cleanLogEntry = `[${timestamp}] ${cleanMessage}`;

    // 写入原始日志文件（保留ANSI码）
    fs.appendFileSync(this.rawLogFile, rawLogEntry + '\n', 'utf8');

    // 写入清理后的日志文件（无ANSI码）
    fs.appendFileSync(this.cleanLogFile, cleanLogEntry + '\n', 'utf8');
  }

  _stripAnsiCodes(text) {
    // 移除ANSI转义码的正则表达式
    return text.replace(/\x1b\[[0-9;]*[a-zA-Z]/g, '');
  }

  _getCapturedLogsForTest(caseId) {
    // 从reporter管理的全局捕获器获取日志
    if (globalLogCapture && globalLogCapture.capturedLogs) {
      const logs = globalLogCapture.capturedLogs;
      console.log(`📊 Found ${logs.length} captured logs from global capture`);
      return logs;
    }

    console.log(`📊 No global log capture available for test ${caseId}`);
    return [];
  }

  _extractModule(ancestorTitles) {
    if (!ancestorTitles || ancestorTitles.length === 0) {
      return 'unknown';
    }

    const title = ancestorTitles[0];
    const moduleMap = {
      'WhatsAppService': 'whatsapp',
      'OrderService': 'order',
      'PaymentService': 'payment',
      'RefundService': 'refund',
      'CustomerService': 'customer',
      'RestaurantService': 'restaurant',
      'GraphQL': 'graphql'
    };

    for (const [key, module] of Object.entries(moduleMap)) {
      if (title.includes(key)) {
        return module;
      }
    }

    return title.toLowerCase().split(' ')[0];
  }
}

module.exports = CustomReporter;
