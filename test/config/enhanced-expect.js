/**
 * Enhanced Expect - 扩展Jest断言以显示Expected/Actual值
 * 符合SOP要求，在所有测试（包括通过的）中显示比较值
 */

const originalExpect = global.expect;

// 全局日志收集器，用于存储断言信息
global.assertionLogs = global.assertionLogs || [];

// 全局程序输出收集器
global.programLogs = global.programLogs || [];

// 全局日志捕获器 - 在模块加载时就开始捕获
const ActLogCapture = require('./actLogCapture');
const globalLogCapture = new ActLogCapture();

// 立即开始捕获
globalLogCapture.startCapture();

// 将捕获器暴露给全局，供reporter使用
global.globalLogCapture = globalLogCapture;

// 存储每个测试的日志
global.testLogsByName = global.testLogsByName || {};

/**
 * 记录断言信息
 */
function logAssertion(matcherName, expected, actual, result, message = '') {
  const assertionInfo = {
    timestamp: Date.now(),
    matcher: matcherName,
    expected: formatValue(expected),
    actual: formatValue(actual),
    result: result ? 'PASS' : 'FAIL',
    message: message
  };
  
  global.assertionLogs.push(assertionInfo);
  
  // 输出断言详情（符合SOP格式）
  if (result) {
    console.log(`    ✓ Assert: ${matcherName}`);
    console.log(`      Expected: ${assertionInfo.expected}`);
    console.log(`      Actual: ${assertionInfo.actual}`);
    console.log(`      Result: PASS`);
  } else {
    console.log(`    ✗ Assert: ${matcherName}`);
    console.log(`      Expected: ${assertionInfo.expected}`);
    console.log(`      Actual: ${assertionInfo.actual}`);
    console.log(`      Result: FAIL - ${message}`);
  }
}

/**
 * 格式化值以便显示
 */
function formatValue(value) {
  if (value === undefined) return 'undefined';
  if (value === null) return 'null';
  if (typeof value === 'string') return `"${value}"`;
  if (typeof value === 'function') return `[Function: ${value.name || 'anonymous'}]`;
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value, null, 2);
    } catch (e) {
      return `[Object: ${value.constructor?.name || 'Unknown'}]`;
    }
  }
  return String(value);
}

/**
 * 包装expect方法以添加日志记录
 */
function wrapExpectMethod(expectInstance) {
  const originalMatchers = {};
  
  // 保存原始匹配器
  const matcherNames = [
    'toBe', 'toEqual', 'toStrictEqual', 'toBeTruthy', 'toBeFalsy',
    'toBeNull', 'toBeUndefined', 'toBeDefined', 'toBeInstanceOf',
    'toBeGreaterThan', 'toBeGreaterThanOrEqual', 'toBeLessThan', 'toBeLessThanOrEqual',
    'toBeCloseTo', 'toMatch', 'toMatchObject', 'toHaveProperty',
    'toContain', 'toContainEqual', 'toHaveLength', 'toThrow', 'toThrowError'
  ];
  
  matcherNames.forEach(matcherName => {
    if (expectInstance[matcherName]) {
      originalMatchers[matcherName] = expectInstance[matcherName];
      
      expectInstance[matcherName] = function(expected) {
        const actual = this.actual !== undefined ? this.actual : this.received;
        let result = false;
        let error = null;
        
        try {
          const matcherResult = originalMatchers[matcherName].call(this, expected);
          result = true;
          logAssertion(matcherName, expected, actual, true);
          return matcherResult;
        } catch (e) {
          result = false;
          error = e;
          logAssertion(matcherName, expected, actual, false, e.message);
          throw e;
        }
      };
    }
  });
  
  // 处理not链式调用
  if (expectInstance.not) {
    const originalNot = expectInstance.not;
    matcherNames.forEach(matcherName => {
      if (originalNot[matcherName]) {
        const originalNotMatcher = originalNot[matcherName];
        originalNot[matcherName] = function(expected) {
          const actual = this.actual !== undefined ? this.actual : this.received;
          let result = false;
          let error = null;
          
          try {
            const matcherResult = originalNotMatcher.call(this, expected);
            result = true;
            logAssertion(`not.${matcherName}`, expected, actual, true);
            return matcherResult;
          } catch (e) {
            result = false;
            error = e;
            logAssertion(`not.${matcherName}`, expected, actual, false, e.message);
            throw e;
          }
        };
      }
    });
  }
  
  return expectInstance;
}

/**
 * 创建增强的expect函数
 */
function createEnhancedExpect() {
  return function enhancedExpect(actual) {
    const expectInstance = originalExpect(actual);
    return wrapExpectMethod(expectInstance);
  };
}

/**
 * 获取当前测试的断言日志
 */
function getAssertionLogs() {
  return global.assertionLogs || [];
}

/**
 * 清理断言日志
 */
function clearAssertionLogs() {
  global.assertionLogs = [];
}

/**
 * 初始化增强的expect
 */
function setupEnhancedExpect() {
  // 替换全局expect
  global.expect = createEnhancedExpect();
  
  // 保留原始expect的静态方法
  Object.keys(originalExpect).forEach(key => {
    if (typeof originalExpect[key] === 'function') {
      global.expect[key] = originalExpect[key];
    }
  });
  
  // 在每个测试开始前清理断言日志并记录测试开始
  beforeEach(() => {
    clearAssertionLogs();

    // 记录当前测试开始时的日志数量
    global.currentTestStartLogCount = globalLogCapture.capturedLogs.length;
    global.currentTestName = expect.getState().currentTestName || 'unknown';
  });

  // 在每个测试结束后保存该测试的日志
  afterEach(() => {
    const testName = global.currentTestName || 'unknown';
    const startCount = global.currentTestStartLogCount || 0;

    // 获取这个测试期间的日志
    const testLogs = globalLogCapture.capturedLogs.slice(startCount);
    global.testLogsByName[testName] = testLogs;
    global.lastCapturedLogs = testLogs;

    console.log(`📝 Test "${testName}" captured ${testLogs.length} logs`);
  });
  
  console.log('✓ Enhanced expect initialized - 断言值显示已启用');
}

// 导出工具函数
module.exports = {
  setupEnhancedExpect,
  getAssertionLogs,
  clearAssertionLogs,
  logAssertion,
  formatValue
};

// 自动初始化
setupEnhancedExpect();
