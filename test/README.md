# Firespoon API Testing Framework

This directory contains the testing framework for the Firespoon API. The framework is built on Jest and provides utilities for testing all aspects of the application, including GraphQL API, WhatsApp integration, and payment processing.

## Table of Contents

- [Getting Started](#getting-started)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Test Helpers](#test-helpers)
- [Test Factories](#test-factories)
- [Fixtures](#fixtures)
- [Mocking External Services](#mocking-external-services)
- [Debugging Tests](#debugging-tests)

## Getting Started

To get started with testing, make sure you have installed all the required dependencies:

```bash
npm install
```

### Database Setup

The testing framework uses **Testcontainers** to manage isolated database instances:

1. **MongoDB 7.0** via testcontainers
   - Automatically creates a fresh MongoDB container for each test run
   - No manual setup required - just ensure Docker is running
   - Uses isolated test database with credentials: `testuser:testpass`

2. **Redis 7.2.4** via testcontainers
   - Automatically creates a fresh Redis container for each test run
   - No manual setup required - just ensure Docker is running
   - Each test run gets a clean Redis instance

**Prerequisites:**
- Docker must be installed and running
- No database configuration needed in `.env.test`

## Test Structure

The test directory is organized as follows:

```
test/
├── cases/                 # Test case definitions (YAML format)
│   ├── unit/              # Unit test case definitions
│   │   ├── controllers/   # Controller test cases
│   │   ├── services/      # Service test cases
│   │   ├── models/        # Model test cases
│   │   ├── utils/         # Utility test cases
│   │   ├── graphql/       # GraphQL resolver test cases
│   │   ├── middleware/    # Middleware test cases
│   │   ├── helpers/       # Helper function test cases
│   │   ├── machines/      # State machine test cases
│   │   └── whatsapp/      # WhatsApp-specific test cases
│   ├── integration/       # Integration test case definitions
│   │   ├── refund_integration_tests.yaml
│   │   ├── refund_e2e_tests.yaml
│   │   └── whatsapp/      # WhatsApp integration test cases
│   ├── e2e/               # End-to-end test case definitions
│   └── performance/       # Performance test case definitions
├── config/                # Test configuration
│   ├── jest.config.js     # Jest configuration
│   ├── globalSetup.js     # Test environment setup
│   └── globalTeardown.js  # Test environment cleanup
├── docs/                  # Test documentation
│   ├── integration/       # Integration test documentation
│   ├── unit/              # Unit test documentation
│   └── README.md          # Test documentation index
├── fixtures/              # Static test data
│   ├── whatsapp/          # WhatsApp webhook payloads
│   └── stripe/            # Stripe webhook payloads
├── factories/             # Test data factories
│   ├── userFactory.js
│   └── restaurantFactory.js
├── helpers/               # Test utilities
│   ├── apiHelper.js       # API request helpers
│   ├── dbHelper.js        # Database operations
│   ├── redisHelper.js     # Redis operations
│   ├── authHelper.js      # Authentication utilities
│   ├── mockHelper.js      # External API mocking
│   └── waitFor.js         # Async utilities
├── unit/                  # Unit test implementations
│   ├── services/
│   ├── models/
│   └── utils/
├── integration/           # Integration test implementations
│   ├── graphql/
│   ├── rest/
│   └── whatsapp/
├── e2e/                   # End-to-end test implementations
├── performance/           # Performance test implementations
├── temp/                  # Temporary files (gitignored)
└── reports/               # Test execution reports
```

## Running Tests

The following npm scripts are available for running tests:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run only end-to-end tests
npm run test:e2e

# Run performance tests
npm run test:performance

# Run basic performance tests (recommended for quick validation)
npm run test:performance:basic

# Run specific performance tests
npm run test:load      # 负载测试
npm run test:stress    # 压力测试
npm run test:concurrency # 并发测试
```

## Test Case Definitions (YAML Format)

The `test/cases/` directory contains test case definitions in YAML format, following the structure defined in the project's testing SOPs. These YAML files serve as the single source of truth for test specifications and are organized by test type.

### YAML Test Case Structure

Each YAML file follows this structure:

```yaml
# File Header (varies by test type)
TestFile: "path/to/source/file.js"  # For unit tests
# OR
Feature: "Feature Name"             # For integration tests
# OR
Scenario: "Scenario Name"           # For e2e tests
# OR
Target: "API: POST /endpoint"       # For performance tests

---
# Test Group
TestFunction: "functionName"
Cases:
  - CaseID: "U-MOD-A01"           # Format: {type}-{module}-{group_id}{sequence_id}
    Module: "auth"
    Description: "Test description"
    Importance: "High"             # High, Medium, Low
    Status: "Implemented"          # Planned, InProgress, Implemented, Failed, Deprecated, Skipped
    Tags:
      - "smoke"
      - "positive"
    Precondition:
      - "Setup condition 1"
      - "Setup condition 2"
    Steps:
      - "Step 1"
      - "Step 2"
    ExpectedResult:
      - "Expected result 1"
      - "Expected result 2"
```

### Test Case ID Format

Test case IDs follow the pattern: `{type}-{module}-{group_id}{sequence_id}`

- **type**: `U` (Unit), `I` (Integration), `E` (E2E), `P` (Performance)
- **module**: Uppercase abbreviation (e.g., AUTH, ORD, PAY)
- **group_id**: Single character (0-9, A-Z)
- **sequence_id**: Two digits (01-99)

Example: `U-AUTH-A01`, `I-ORD-B15`, `E-PAY-C03`

### Available Tags

- **Functional**: `smoke`, `regression`, `security`
- **Type**: `positive`, `negative`, `boundary`, `error-handling`
- **Performance**: `performance`, `stress`, `concurrency`

## Writing Tests

### Unit Tests

Unit tests focus on testing individual functions, classes, or modules in isolation. External dependencies should be mocked.

Example:

```javascript
// test/unit/services/orderService.test.js
const orderService = require('../../../services/orderService');
const Order = require('../../../models/order');

// Mock the Order model
jest.mock('../../../models/order');

describe('Order Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should calculate order total correctly', () => {
    // Test implementation
  });
});
```

### Integration Tests

Integration tests verify that different parts of the application work together correctly. These tests use the real test database and Redis, but mock external services.

Example:

```javascript
// test/integration/graphql/orderMutations.test.js
const { connectTestDB, clearCollections } = require('../../helpers/dbHelper');
const { generateAuthToken } = require('../../helpers/authHelper');
const { graphqlMutation } = require('../../helpers/apiHelper');
const UserFactory = require('../../factories/userFactory');

describe('Order GraphQL Mutations', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  beforeEach(async () => {
    await clearCollections('User', 'Restaurant', 'Order');
  });

  test('should create a new order', async () => {
    // Test implementation
  });
});
```

### End-to-End Tests

End-to-end tests simulate real user scenarios and test the entire application flow. These tests should be focused on critical user journeys.

Example:

```javascript
// test/e2e/orderFlow.test.js
const { connectTestDB, clearCollections } = require('../helpers/dbHelper');
const { generateAuthToken } = require('../helpers/authHelper');
const { graphqlMutation } = require('../helpers/apiHelper');
const { mockStripeCheckoutCreate, mockWhatsAppSend } = require('../helpers/mockHelper');

describe('Order Flow', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  beforeEach(async () => {
    await clearCollections('User', 'Restaurant', 'Order');
  });

  test('should complete full order flow from creation to delivery', async () => {
    // Test implementation
  });
});
```

### Performance Tests

Performance tests evaluate the system's performance under various load conditions. These tests use Autocannon for HTTP load testing and include load testing, stress testing, and concurrency testing.

**性能测试类型：**

1. **负载测试 (Load Testing)** - `npm run test:load`
   - 测试系统在正常负载下的性能表现
   - 验证响应时间、吞吐量和错误率
   - 包括内存泄漏检测和资源使用监控

2. **压力测试 (Stress Testing)** - `npm run test:stress`
   - 测试系统在极限负载下的表现
   - 验证系统的恢复能力和稳定性
   - 包括突发流量和资源耗尽测试

3. **并发测试 (Concurrency Testing)** - `npm run test:concurrency`
   - 测试并发操作的数据一致性
   - 验证数据库事务的原子性
   - 包括竞态条件和死锁检测

**性能测试配置：**
- 测试环境：使用 Testcontainers 启动真实的 MongoDB 和 Redis
- 超时设置：120秒（适应性能测试的长时间运行）
- 并发控制：串行执行避免资源竞争
- 报告生成：自动生成性能测试报告和摘要

Example:

```javascript
// test/performance/load.test.js
const autocannon = require('autocannon');
const { connectTestDB, disconnectTestDB } = require('../helpers/testDatabase');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');

describe('Load Testing', () => {
  let testApp;
  let baseURL;

  beforeAll(async () => {
    await connectTestDB({ useRealDatabase: true });
    testApp = await createTestApp();
    baseURL = `http://localhost:${testApp.port}`;
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  test('should handle GraphQL queries under load', async () => {
    const query = `
      query {
        restaurants {
          _id
          name
          isActive
        }
      }
    `;

    const result = await autocannon({
      url: `${baseURL}/graphql`,
      method: 'POST',
      connections: 50,
      duration: 10,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query })
    });

    expect(result.errors).toBe(0);
    expect(result.timeouts).toBe(0);
    expect(result.latency.mean).toBeLessThan(1000);
  });
});
```

## Test Helpers

The test framework provides several helper modules to simplify testing:

### Database Helper (`dbHelper.js`)

Provides functions for database operations with MongoDB Atlas:

```javascript
const { connectTestDB, clearCollections, findById, create } = require('../helpers/dbHelper');

// Connect to MongoDB Atlas 8.0.8
await connectTestDB();

// Clear specific collections (only clears test data to avoid affecting production data)
await clearCollections('User', 'Restaurant');

// Find a document by ID
const user = await findById('User', userId);

// Create a document (automatically marks the document as test data)
const newUser = await create('User', { name: 'Test User', email: '<EMAIL>' });

// Create a document without test markers (use with caution)
const productionLikeUser = await create('User', userData, false);
```

### Redis Helper (`redisHelper.js`)

Provides functions for Redis operations:

```javascript
const { getRedisClient, clearRedis, getSession } = require('../helpers/redisHelper');

// Clear Redis
await clearRedis();

// Get a session
const session = await getSession(sessionId);
```

### API Helper (`apiHelper.js`)

Provides functions for API requests:

```javascript
const { graphqlQuery, whatsappWebhook } = require('../helpers/apiHelper');

// Send a GraphQL query
const response = await graphqlQuery(query, variables, token);

// Send a WhatsApp webhook request
const response = await whatsappWebhook(payload, signature);
```

### Authentication Helper (`authHelper.js`)

Provides functions for authentication:

```javascript
const { generateAuthToken, generateXMMCSignature } = require('../helpers/authHelper');

// Generate a JWT token
const token = generateAuthToken(user);

// Generate a WhatsApp webhook signature
const signature = generateXMMCSignature(payload, secret);
```

### Mock Helper (`mockHelper.js`)

Provides functions for mocking external services:

```javascript
const { mockWhatsAppSend, mockStripeCheckoutCreate } = require('../helpers/mockHelper');

// Mock WhatsApp API
mockWhatsAppSend();

// Mock Stripe API
mockStripeCheckoutCreate();
```

### Async Helper (`waitFor.js`)

Provides functions for handling asynchronous operations:

```javascript
const { delay, waitForDbRecord, waitForRedisKey } = require('../helpers/waitFor');

// Wait for a database record
const order = await waitForDbRecord('Order', { _id: orderId }, record => record.status === 'PAID');

// Wait for a Redis key
const session = await waitForRedisKey(sessionKey, value => value !== null);
```

## Test Factories

Test factories provide a convenient way to create test data:

```javascript
const UserFactory = require('../factories/userFactory');
const RestaurantFactory = require('../factories/restaurantFactory');

// Create a user
const user = await UserFactory.create();

// Create a restaurant
const restaurant = await RestaurantFactory.create({ owner: user._id });
```

## Fixtures

Fixtures provide static test data for webhooks and other external inputs:

```javascript
const messageReceivedPayload = require('../fixtures/whatsapp/message-received.json');
const checkoutSessionCompletedPayload = require('../fixtures/stripe/checkout-session-completed.json');
```

## Mocking External Services

External services like WhatsApp and Stripe should be mocked in tests:

```javascript
const { mockWhatsAppSend, mockStripeCheckoutCreate } = require('../helpers/mockHelper');

// Mock WhatsApp API
mockWhatsAppSend({
  data: { id: 'mock-message-id' }
});

// Mock Stripe API
mockStripeCheckoutCreate({
  id: 'mock-session-id',
  url: 'https://checkout.stripe.com/mock'
});
```

## Debugging Tests

To debug tests, you can use the following techniques:

1. **Run a single test file**:

```bash
npx jest test/integration/graphql/orderMutations.test.js
```

2. **Run a specific test**:

```bash
npx jest -t "should create a new order"
```

3. **Enable verbose output**:

```bash
npx jest --verbose
```

4. **Increase timeout for slow tests**:

```javascript
jest.setTimeout(30000); // 30 seconds
```

5. **Debug with Node.js inspector**:

```bash
node --inspect-brk node_modules/.bin/jest test/integration/graphql/orderMutations.test.js
```

Then open Chrome and navigate to `chrome://inspect` to attach the debugger.

## MongoDB Atlas Configuration

### Setting Up MongoDB Atlas 8.0.8 for Testing

1. **Create a Test Database**:
   - Create a dedicated database for testing in your Atlas cluster
   - Use a clear naming convention (e.g., `firespoon_test`)

2. **Create a Test User**:
   - Create a dedicated database user with read/write permissions to the test database
   - Use a strong password and store it securely

3. **Configure Network Access**:
   - Add your development machine's IP address to the Atlas network access list
   - For CI/CD environments, you may need to allow access from those IP ranges

4. **Configure Connection String**:
   - Copy your connection string from Atlas
   - Replace the database name with your test database name
   - Add the connection string to your `.env.test` file:

```
CONNECTION_STRING=mongodb+srv://testuser:<EMAIL>/firespoon_test?retryWrites=true&w=majority
```

### Safety Features for Testing

The testing framework includes several safety features:

1. **Test Data Marking**:
   - All test data is automatically marked with `_testData: true`
   - Names and emails are prefixed with `test_`
   - This makes it easy to identify and clean up test data

2. **Selective Cleanup**:
   - `clearCollections()` and `clearDatabase()` only remove test-marked data
   - This prevents accidental deletion of production data
