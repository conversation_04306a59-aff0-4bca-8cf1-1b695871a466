/**
 * 测试日志捕获功能
 * 验证console和winston输出是否被正确捕获
 */

const logger = require('../../helpers/logger');

describe('Log Capture Test', () => {
  test('should capture console.log output', () => {
    console.log('This is a test console.log message');
    console.warn('This is a test console.warn message');
    console.error('This is a test console.error message');
    
    expect(true).toBe(true);
  });

  test('should capture winston logger output', () => {
    logger.info('This is a test winston info message');
    logger.warn('This is a test winston warn message');
    logger.error('This is a test winston error message');
    
    expect(true).toBe(true);
  });

  test('should capture mixed console and winston output', () => {
    console.log('Console: Starting test execution');
    logger.info('Winston: Test initialization complete');
    
    const testData = { id: 123, name: 'Test User' };
    console.log('Console: Test data:', testData);
    logger.debug('<PERSON>: Processing test data', testData);
    
    console.log('Console: Test execution finished');
    logger.info('<PERSON>: Test completed successfully');
    
    expect(testData.id).toBe(123);
    expect(testData.name).toBe('Test User');
  });

  test('should capture error scenarios', () => {
    try {
      console.log('Console: Attempting risky operation');
      logger.warn('Winston: Warning - risky operation detected');
      
      // Simulate an error scenario
      const result = JSON.parse('{"valid": "json"}');
      console.log('Console: JSON parsing successful:', result);
      logger.info('Winston: Operation completed without errors');
      
      expect(result.valid).toBe('json');
    } catch (error) {
      console.error('Console: Error occurred:', error.message);
      logger.error('Winston: Exception caught', error);
      throw error;
    }
  });
});
