/**
 * Order Model refundStatus 高级测试
 * 
 * 实现以下测试用例：
 * - R-STATUS-004: 全额退款后refundStatus状态更新
 * - R-STATUS-006: GraphQL查询refundStatus字段  
 * - R-STATUS-007: refundStatus与totalRefunded数据一致性
 */

const { connectTestDB, disconnectTestDB, clearTestDB } = require('../../helpers/testDatabase');
const Order = require('../../../models/order');
const mongoose = require('mongoose');

describe('Order Model - refundStatus Advanced Tests', () => {
  beforeAll(async () => {
    await connectTestDB();
    console.log('Advanced refundStatus tests initialized');
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  // R-STATUS-004: 全额退款后refundStatus状态更新
  test('R-STATUS-004: should update refundStatus to FULL after full refund', async () => {
    console.log('📝 Testing R-STATUS-004: 全额退款后refundStatus状态更新');
    
    // 创建订单数据
    const orderData = {
      orderId: `FULL-REFUND-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: '507f1f77bcf86cd799439011',
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012',
      customerId: 'CUST-FULL-001',
      customerPhone: '+1234567890',
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'ADDR-FULL-001',
      items: [{
        title: 'Test Item',
        quantity: 1,
        variation: { title: 'Regular', price: 100.00 }
      }],
      orderStatus: 'PAID',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00
    };

    // 创建并保存订单
    const order = new Order(orderData);
    await order.save();

    // 验证初始状态
    expect(order.refundStatus).toBe('NONE');
    expect(order.totalRefunded).toBe(0);
    console.log('✅ 初始状态验证通过: refundStatus=NONE, totalRefunded=0');

    // 模拟全额退款业务逻辑
    order.totalRefunded = 100.00;
    order.refundStatus = 'FULL';
    order.orderStatus = 'CANCELLED';
    
    // 添加退款记录引用（模拟）
    const mockRefundId = new mongoose.Types.ObjectId();
    order.refunds.push(mockRefundId);

    await order.save();

    // 验证全额退款后的状态
    const updatedOrder = await Order.findById(order._id);
    expect(updatedOrder.refundStatus).toBe('FULL');
    expect(updatedOrder.totalRefunded).toBe(100.00);
    expect(updatedOrder.orderStatus).toBe('CANCELLED');
    expect(updatedOrder.refunds.length).toBe(1);
    
    console.log('✅ 全额退款状态验证通过:');
    console.log(`  refundStatus: ${updatedOrder.refundStatus}`);
    console.log(`  totalRefunded: ${updatedOrder.totalRefunded}`);
    console.log(`  orderStatus: ${updatedOrder.orderStatus}`);
    console.log(`  refunds count: ${updatedOrder.refunds.length}`);
  });

  // R-STATUS-006: GraphQL查询refundStatus字段
  test('R-STATUS-006: should support GraphQL refundStatus field queries', async () => {
    console.log('📝 Testing R-STATUS-006: GraphQL查询refundStatus字段');
    
    // 创建不同refundStatus状态的订单
    const orders = [];
    
    // 创建NONE状态订单
    const noneOrder = new Order({
      orderId: `NONE-${Date.now()}`,
      orderAmount: 50.00,
      restaurantId: '507f1f77bcf86cd799439011',
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012',
      customerId: 'CUST-NONE-001',
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'ADDR-NONE-001',
      items: [{ title: 'Item', quantity: 1, variation: { title: 'Regular', price: 50.00 } }],
      orderStatus: 'PAID',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 50.00
    });
    await noneOrder.save();
    orders.push(noneOrder);

    // 创建PARTIAL状态订单
    const partialOrder = new Order({
      orderId: `PARTIAL-${Date.now()}`,
      orderAmount: 80.00,
      restaurantId: '507f1f77bcf86cd799439011',
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012',
      customerId: 'CUST-PARTIAL-001',
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'ADDR-PARTIAL-001',
      items: [{ title: 'Item', quantity: 1, variation: { title: 'Regular', price: 80.00 } }],
      orderStatus: 'PARTIALLY_REFUNDED',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 80.00,
      refundStatus: 'PARTIAL',
      totalRefunded: 30.00
    });
    await partialOrder.save();
    orders.push(partialOrder);

    // 创建FULL状态订单
    const fullOrder = new Order({
      orderId: `FULL-${Date.now()}`,
      orderAmount: 60.00,
      restaurantId: '507f1f77bcf86cd799439011',
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012',
      customerId: 'CUST-FULL-001',
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'ADDR-FULL-001',
      items: [{ title: 'Item', quantity: 1, variation: { title: 'Regular', price: 60.00 } }],
      orderStatus: 'CANCELLED',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 60.00,
      refundStatus: 'FULL',
      totalRefunded: 60.00
    });
    await fullOrder.save();
    orders.push(fullOrder);

    // 验证不同状态的订单
    const noneOrders = await Order.find({ refundStatus: 'NONE' });
    const partialOrders = await Order.find({ refundStatus: 'PARTIAL' });
    const fullOrders = await Order.find({ refundStatus: 'FULL' });

    expect(noneOrders.length).toBe(1);
    expect(partialOrders.length).toBe(1);
    expect(fullOrders.length).toBe(1);

    // 验证每个状态的订单数据正确性
    expect(noneOrders[0].refundStatus).toBe('NONE');
    expect(noneOrders[0].totalRefunded).toBe(0);

    expect(partialOrders[0].refundStatus).toBe('PARTIAL');
    expect(partialOrders[0].totalRefunded).toBe(30.00);

    expect(fullOrders[0].refundStatus).toBe('FULL');
    expect(fullOrders[0].totalRefunded).toBe(60.00);

    console.log('✅ GraphQL查询refundStatus字段验证通过:');
    console.log(`  NONE状态订单: ${noneOrders.length}个`);
    console.log(`  PARTIAL状态订单: ${partialOrders.length}个`);
    console.log(`  FULL状态订单: ${fullOrders.length}个`);

    // 验证枚举值在查询中的使用
    const allRefundStatuses = await Order.distinct('refundStatus');
    expect(allRefundStatuses).toContain('NONE');
    expect(allRefundStatuses).toContain('PARTIAL');
    expect(allRefundStatuses).toContain('FULL');
    
    console.log('✅ 枚举值查询验证通过:', allRefundStatuses);
  });

  // R-STATUS-007: refundStatus与totalRefunded数据一致性
  test('R-STATUS-007: should maintain consistency between refundStatus and totalRefunded', async () => {
    console.log('📝 Testing R-STATUS-007: refundStatus与totalRefunded数据一致性');
    
    // 创建测试订单
    const orderData = {
      orderId: `CONSISTENCY-${Date.now()}`,
      orderAmount: 100.00,
      restaurantId: '507f1f77bcf86cd799439011',
      restaurantName: 'Test Restaurant',
      restaurantBrand: 'Test Brand',
      restaurantBrandId: '507f1f77bcf86cd799439012',
      customerId: 'CUST-CONSISTENCY-001',
      deliveryAddress: 'Test Address',
      deliveryAddressId: 'ADDR-CONSISTENCY-001',
      items: [{ title: 'Item', quantity: 1, variation: { title: 'Regular', price: 100.00 } }],
      orderStatus: 'PAID',
      paymentMethod: 'STRIPE',
      paymentStatus: 'PAID',
      paidAmount: 100.00
    };

    const order = new Order(orderData);
    await order.save();

    // 验证初始一致性：NONE状态
    expect(order.refundStatus).toBe('NONE');
    expect(order.totalRefunded).toBe(0);
    console.log('✅ 初始一致性验证: NONE状态，totalRefunded=0');

    // 测试PARTIAL状态一致性
    order.totalRefunded = 30.00;
    order.refundStatus = 'PARTIAL';
    await order.save();

    let updatedOrder = await Order.findById(order._id);
    expect(updatedOrder.refundStatus).toBe('PARTIAL');
    expect(updatedOrder.totalRefunded).toBe(30.00);
    expect(updatedOrder.totalRefunded).toBeGreaterThan(0);
    expect(updatedOrder.totalRefunded).toBeLessThan(updatedOrder.orderAmount);
    console.log('✅ PARTIAL状态一致性验证: 0 < totalRefunded < orderAmount');

    // 测试FULL状态一致性
    order.totalRefunded = 100.00;
    order.refundStatus = 'FULL';
    await order.save();

    updatedOrder = await Order.findById(order._id);
    expect(updatedOrder.refundStatus).toBe('FULL');
    expect(updatedOrder.totalRefunded).toBe(100.00);
    expect(updatedOrder.totalRefunded).toBeGreaterThanOrEqual(updatedOrder.orderAmount);
    console.log('✅ FULL状态一致性验证: totalRefunded >= orderAmount');

    // 批量验证数据一致性
    const allOrders = await Order.find({});
    let consistencyErrors = [];

    for (const orderDoc of allOrders) {
      if (orderDoc.refundStatus === 'NONE' && orderDoc.totalRefunded !== 0) {
        consistencyErrors.push(`Order ${orderDoc.orderId}: NONE状态但totalRefunded=${orderDoc.totalRefunded}`);
      }
      
      if (orderDoc.refundStatus === 'PARTIAL' && 
          (orderDoc.totalRefunded <= 0 || orderDoc.totalRefunded >= orderDoc.orderAmount)) {
        consistencyErrors.push(`Order ${orderDoc.orderId}: PARTIAL状态但totalRefunded=${orderDoc.totalRefunded}`);
      }
      
      if (orderDoc.refundStatus === 'FULL' && orderDoc.totalRefunded < orderDoc.orderAmount) {
        consistencyErrors.push(`Order ${orderDoc.orderId}: FULL状态但totalRefunded=${orderDoc.totalRefunded} < orderAmount=${orderDoc.orderAmount}`);
      }
    }

    expect(consistencyErrors).toHaveLength(0);
    console.log('✅ 批量数据一致性验证通过，无一致性错误');
    console.log(`  检查了 ${allOrders.length} 个订单`);
    
    // 验证业务规则
    const noneCount = await Order.countDocuments({ refundStatus: 'NONE', totalRefunded: 0 });
    const partialCount = await Order.countDocuments({ 
      refundStatus: 'PARTIAL', 
      totalRefunded: { $gt: 0, $lt: 100 } 
    });
    const fullCount = await Order.countDocuments({ 
      refundStatus: 'FULL', 
      totalRefunded: { $gte: 100 } 
    });

    console.log('✅ 业务规则验证:');
    console.log(`  NONE状态且totalRefunded=0: ${noneCount}个`);
    console.log(`  PARTIAL状态且0<totalRefunded<100: ${partialCount}个`);
    console.log(`  FULL状态且totalRefunded>=100: ${fullCount}个`);
  });
});
