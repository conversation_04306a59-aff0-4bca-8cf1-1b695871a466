/**
 * 简单的日志测试 - 验证日志捕获
 */

describe('Simple Log Test', () => {
  test('should show console and winston output in logs', () => {
    // 直接输出到console
    console.log('=== CONSOLE TEST START ===');
    console.log('This is a console.log message');
    console.warn('This is a console.warn message');
    console.error('This is a console.error message');
    console.log('=== CONSOLE TEST END ===');
    
    // 输出到winston
    const logger = require('../../helpers/logger');
    logger.info('=== WINSTON TEST START ===');
    logger.info('This is a winston info message');
    logger.warn('This is a winston warn message');
    logger.error('This is a winston error message');
    logger.info('=== WINSTON TEST END ===');
    
    // 简单断言
    expect(1 + 1).toBe(2);
    expect('hello').toBe('hello');
  });
});
