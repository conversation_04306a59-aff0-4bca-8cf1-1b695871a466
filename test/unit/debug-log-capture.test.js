/**
 * 调试日志捕获功能
 */

describe('Debug Log Capture', () => {
  test('should debug log capture mechanism', () => {
    console.log('=== DEBUG TEST START ===');
    
    // 检查全局变量
    console.log('Global programLogs:', global.programLogs?.length || 0);
    console.log('Global testLogsByName:', Object.keys(global.testLogsByName || {}).length);
    console.log('Global lastCapturedLogs:', global.lastCapturedLogs?.length || 0);
    
    // 直接测试ActLogCapture
    const ActLogCapture = require('../config/actLogCapture');
    const testCapture = new ActLogCapture();
    
    console.log('Starting manual capture...');
    testCapture.startCapture();
    
    console.log('This should be captured!');
    console.warn('This warning should be captured!');
    
    const logger = require('../../helpers/logger');
    logger.info('This winston log should be captured!');
    
    const captured = testCapture.stopCapture();
    console.log('Manual capture stopped. Captured logs:', captured.length);
    
    captured.forEach((log, index) => {
      console.log(`Log ${index}: [${log.type}] ${log.message}`);
    });
    
    console.log('=== DEBUG TEST END ===');
    
    expect(captured.length).toBeGreaterThan(0);
  });
});
