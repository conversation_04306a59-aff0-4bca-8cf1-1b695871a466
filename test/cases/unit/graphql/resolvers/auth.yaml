TestFile: graphql/resolvers/auth.js
---
TestFunction: login (Query)
Cases:
  - CaseID: "U-GQL-AUTH-101"
    Module: "authResolver"
    Description: "Should log in a user with correct credentials and return a token"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "auth"
      - "login"
    Precondition:
      - "A user exists in the database with a known email and hashed password."
      - "Mock `User.findOne` to return the user document."
      - "Mock `bcrypt.compare` to return true."
      - "Mock `jwt.sign` to return a predictable token."
    Steps:
      - "1. Call the `login` resolver with the correct email and password."
    ExpectedResult:
      - "Returns an object containing `token`, `userId`, and `tokenExpiration`."
      - "The returned token matches the mocked token."
  - CaseID: "U-GQL-AUTH-102"
    Module: "authResolver"
    Description: "Should throw an error for a non-existent user"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
    Precondition:
      - "Mock `User.findOne` to return null."
    Steps:
      - "1. Call `login` with an email that does not exist."
    ExpectedResult:
      - "Throws an error with the message 'A user with this email could not be found.'."
  - CaseID: "U-GQL-AUTH-103"
    Module: "authResolver"
    Description: "Should throw an error for an incorrect password"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "auth"
    Precondition:
      - "Mock `User.findOne` to return a user document."
      - "Mock `bcrypt.compare` to return false."
    Steps:
      - "1. Call `login` with a correct email but incorrect password."
    ExpectedResult:
      - "Throws an error with the message 'Wrong password!'."
---
TestFunction: createUser (Mutation)
Cases:
  - CaseID: "U-GQL-AUTH-201"
    Module: "authResolver"
    Description: "Should create a new user successfully"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "auth"
      - "register"
    Precondition:
      - "Valid `userInput` is provided."
      - "Mock `User.findOne` to return null (email not taken)."
      - "Mock `bcrypt.hash` to return a predictable hashed password."
      - "Mock the `save` method of the User model."
    Steps:
      - "1. Call `createUser` with `userInput`."
    ExpectedResult:
      - "`bcrypt.hash` is called with the provided password."
      - "A new `User` is created with the hashed password."
      - "The `save` method is called."
      - "Returns the newly created user object (excluding password)."
  - CaseID: "U-GQL-AUTH-202"
    Module: "authResolver"
    Description: "Should throw an error if email already exists"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "Mock `User.findOne` to return an existing user document."
    Steps:
      - "1. Call `createUser` with an email that is already registered."
    ExpectedResult:
      - "Throws an error with the message 'User exists already!'."
---
TestFunction: requestPasswordReset (Mutation)
Cases:
  - CaseID: "U-GQL-AUTH-301"
    Module: "authResolver"
    Description: "Should generate a reset token and send a reset email"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "password-reset"
    Precondition:
      - "A user exists with the provided email."
      - "Mock `User.findOne` to return this user."
      - "Mock the `save` method on the user document."
      - "Mock the email sending service (e.g., mailgun, sendgrid) to resolve successfully."
    Steps:
      - "1. Call `requestPasswordReset` with the user's email."
    ExpectedResult:
      - "A `resetToken` and `resetTokenExpiration` are set on the user document."
      - "The `save` method is called."
      - "The email service is called with the user's email and the generated token."
      - "Returns an object with `{ success: true, message: 'Password reset link sent.' }`."
