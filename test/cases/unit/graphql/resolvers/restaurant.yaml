TestFile: graphql/resolvers/restaurant.js
---
TestFunction: restaurants (Query)
Cases:
  - CaseID: "U-GQL-RES-101"
    Module: "restaurantResolver"
    Description: "Should fetch nearby restaurants using geospatial query when location is provided"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "restaurant"
      - "geospatial"
    Precondition:
      - "`latitude` and `longitude` are provided in the query."
      - "Mock `Restaurant.find` to be called with a `$nearSphere` query on the `location` field."
    Steps:
      - "1. Call the `restaurants` resolver with latitude and longitude."
    ExpectedResult:
      - "`Restaurant.find` is called with a query containing `$nearSphere` with the correct coordinates and `maxDistance`."
      - "Returns a paginated list of transformed restaurant objects."
  - CaseID: "U-GQL-RES-102"
    Module: "restaurantResolver"
    Description: "Should fetch restaurants using text search and other filters"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "filter"
      - "search"
    Precondition:
      - "`name`, `cuisine`, and `rating` arguments are provided."
      - "Mock `Restaurant.find` to be called with a query combining `$text` search for name, cuisine filter, and rating filter."
    Steps:
      - "1. Call the `restaurants` resolver with various filters but no location."
    ExpectedResult:
      - "`Restaurant.find` is called with a query containing `$text: { $search: ... }`, `cuisine`, and `rating: { $gte: ... }`."
      - "Returns a paginated list of results."
---
TestFunction: restaurantLogin (Mutation)
Cases:
  - CaseID: "U-GQL-RES-201"
    Module: "restaurantResolver"
    Description: "Should log in a restaurant with correct credentials and return a token"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "authentication"
      - "login"
    Precondition:
      - "Valid `email` and `password` are provided."
      - "Mock `Restaurant.findOne` to return a restaurant document with a hashed password."
      - "Mock `bcrypt.compare` to return `true`."
      - "Mock `jwt.sign`."
    Steps:
      - "1. Call `restaurantLogin` with correct credentials."
    ExpectedResult:
      - "`bcrypt.compare` is called and returns true."
      - "`jwt.sign` is called with the correct payload (userId, email, role: 'RESTAURANT')."
      - "Returns an object containing `userId`, `token`, and `tokenExpiration`."
---
TestFunction: createRestaurant (Mutation)
Cases:
  - CaseID: "U-GQL-RES-301"
    Module: "restaurantResolver"
    Description: "Should create a new restaurant and hash the password"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "registration"
    Precondition:
      - "Valid `restaurantInput` is provided."
      - "Mock `Restaurant.findOne` to return `null` (no existing user)."
      - "Mock `bcrypt.hash` to return a hashed password."
      - "Mock the `save` method of the Restaurant model."
    Steps:
      - "1. Call `createRestaurant`."
    ExpectedResult:
      - "`bcrypt.hash` is called with the provided password."
      - "A new `Restaurant` instance is created with the hashed password."
      - "The `save` method is called."
      - "Returns the transformed new restaurant object."
---
TestFunction: transformRestaurant (Helper)
Cases:
  - CaseID: "U-GQL-RES-401"
    Module: "restaurantResolver"
    Description: "Should correctly determine if a restaurant is available based on current time and opening hours"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "transform"
      - "business-logic"
    Precondition:
      - "A mock restaurant document is provided with `openingHrs` and `closingHrs`."
      - "The current time is within the opening hours."
    Steps:
      - "1. Call `transformRestaurant` with the mock document."
    ExpectedResult:
      - "The returned object has `isAvailable` set to `true`."
  - CaseID: "U-GQL-RES-402"
    Module: "restaurantResolver"
    Description: "Should correctly determine if a restaurant is unavailable"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "transform"
      - "business-logic"
    Precondition:
      - "A mock restaurant document is provided with `openingHrs` and `closingHrs`."
      - "The current time is outside the opening hours."
    Steps:
      - "1. Call `transformRestaurant` with the mock document."
    ExpectedResult:
      - "The returned object has `isAvailable` set to `false`."
