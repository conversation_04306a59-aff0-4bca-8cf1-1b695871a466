TestFile: graphql/resolvers/food.js
---
TestFunction: foods (Query)
Cases:
  - CaseID: "U-GQL-FOD-101"
    Module: "foodResolver"
    Description: "Should fetch a paginated list of foods with multiple filters applied"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "food"
      - "filter"
      - "pagination"
    Precondition:
      - "Query arguments for `restaurant`, `category`, `vegetarian`, `name` (search), `page`, and `limit` are provided."
      - "Mock `Food.find` to be called with a complex query object combining all filters."
      - "Mock `Food.countDocuments` to be called with the same query object."
    Steps:
      - "1. Call the `foods` resolver with a combination of filter and pagination arguments."
    ExpectedResult:
      - "`Food.find` is called with the correct filters (e.g., `restaurant`, `category`, `vegetarian`, `name: /.../i`)."
      - "The find query includes correct `.sort().skip().limit()` chaining for pagination."
      - "Returns an object with a `foods` array of transformed food objects and a `total` count."
---
TestFunction: createFood (Mutation)
Cases:
  - CaseID: "U-GQL-FOD-201"
    Module: "foodResolver"
    Description: "Should create a new food and add its ID to the parent category"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "food"
      - "relationship"
    Precondition:
      - "User is authenticated."
      - "Valid `foodInput` is provided, including a `category` ID."
      - "Mock `Food.create` to return the new food document."
      - "Mock `Category.findById` to return a category document."
      - "Mock the `save` method on the found category document."
    Steps:
      - "1. Call `createFood` with `foodInput`."
    ExpectedResult:
      - "`Food.create` is called with the input data."
      - "`Category.findById` is called to retrieve the parent category."
      - "The new food's ID is pushed to the category's `foods` array."
      - "The category's `save` method is called."
      - "Returns the transformed new food object."
---
TestFunction: deleteFood (Mutation)
Cases:
  - CaseID: "U-GQL-FOD-301"
    Module: "foodResolver"
    Description: "Should delete a food and remove its ID from the parent category"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "mutation"
      - "food"
      - "relationship"
    Precondition:
      - "User is authenticated."
      - "A valid food `id` is provided."
      - "Mock `Food.findById` to return a food document with a populated category."
      - "Mock `Food.findByIdAndRemove` to resolve successfully."
      - "Mock `Category.findById` to return the parent category document."
      - "Mock the `save` method on the parent category document."
    Steps:
      - "1. Call `deleteFood` with the food ID."
    ExpectedResult:
      - "`Food.findByIdAndRemove` is called."
      - "The food's ID is removed from the parent category's `foods` array."
      - "The category's `save` method is called."
      - "Returns `true`."
  - CaseID: "U-GQL-FOD-302"
    Module: "foodResolver"
    Description: "Should throw an error if the food to delete is not found"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "not-found"
    Precondition:
      - "User is authenticated."
      - "Mock `Food.findById` to return null."
    Steps:
      - "1. Call `deleteFood` with a non-existent food ID."
    ExpectedResult:
      - "Throws an error with the message 'Food not found'."
