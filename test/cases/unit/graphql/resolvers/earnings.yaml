TestFile: graphql/resolvers/earnings.js
---
TestFunction: earnings (Query)
Cases:
  - CaseID: "U-GQL-ERN-101"
    Module: "earningsResolver"
    Description: "Should fetch a paginated list of earnings for the authenticated user"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
      - "earnings"
      - "pagination"
    Precondition:
      - "User is authenticated (req.isAuth = true, req.userId is set)."
      - "`page` and `limit` arguments are provided."
      - "Mock `Earnings.find` to return an array of earning documents, checking for correct user filter, sort, skip, and limit options."
      - "Mock `Earnings.countDocuments` to return a total count."
    Steps:
      - "1. Call the `earnings` resolver with `page` and `limit` arguments."
    ExpectedResult:
      - "`Earnings.find` and `Earnings.countDocuments` are called with `{ user: req.userId }`."
      - "The resolver returns an object with an array of transformed earnings and the total count."
  - CaseID: "U-GQL-ERN-102"
    Module: "earningsResolver"
    Description: "Should return an empty list and zero total if the user has no earnings"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "edge-case"
      - "zero-state"
    Precondition:
      - "User is authenticated."
      - "Mock `Earnings.find` to return an empty array `[]`."
      - "Mock `Earnings.countDocuments` to return 0."
    Steps:
      - "1. Call the `earnings` resolver."
    ExpectedResult:
      - "Returns `{ earnings: [], total: 0 }`."
  - CaseID: "U-GQL-ERN-103"
    Module: "earningsResolver"
    Description: "Should throw an error if the user is not authenticated"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "authentication"
    Precondition:
      - "The request context's `isAuth` property is `false`."
    Steps:
      - "1. Call the `earnings` resolver."
    ExpectedResult:
      - "Throws an error with the message 'Not authenticated!'."
