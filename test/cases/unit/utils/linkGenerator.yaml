TestFile: whatsapp/utils/linkGenerator.js
---
TestFunction: generateMenuLink
Cases:
  - CaseID: "U-LNK-A01"
    Module: "linkGenerator"
    Description: "Should generate menu link with token and orderURL"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "menu"
      - "url"
    Precondition:
      - "Valid token and orderURL are provided"
      - "linkGenerator module is available"
    Steps:
      - "Call linkGenerator.generateMenuLink(token, orderURL)"
      - "Verify the generated URL format"
    ExpectedResult:
      - "Returns URL in format: https://{orderURL}/m/{token}/"
  - CaseID: "U-LNK-A02"
    Module: "linkGenerator"
    Description: "Should handle different domains"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "domain"
      - "url"
    Precondition:
      - "Different domain URLs are provided"
    Steps:
      - "Call generateMenuLink with different orderURL domains"
      - "Verify URL generation works for various domains"
    ExpectedResult:
      - "Correctly generates URLs for different domains"
  - CaseID: "U-LNK-A03"
    Module: "linkGenerator"
    Description: "Should handle special characters in token"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "special-chars"
      - "url"
    Precondition:
      - "Token with special characters is provided"
    Steps:
      - "Call generateMenuLink with token containing special characters"
      - "Verify URL generation handles special characters"
    ExpectedResult:
      - "Correctly generates URL with special characters in token"
---
TestFunction: generateAddressLink
Cases:
  - CaseID: "U-LNK-B01"
    Module: "linkGenerator"
    Description: "Should generate address link with token and orderURL"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "address"
      - "url"
    Precondition:
      - "Valid token and orderURL are provided"
      - "linkGenerator module is available"
    Steps:
      - "Call linkGenerator.generateAddressLink(token, orderURL)"
      - "Verify the generated URL format"
    ExpectedResult:
      - "Returns URL in format: https://{orderURL}/a/{token}/"
  - CaseID: "U-LNK-B02"
    Module: "linkGenerator"
    Description: "Should handle different domains for address links"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "domain"
      - "url"
    Precondition:
      - "Different domain URLs are provided"
    Steps:
      - "Call generateAddressLink with different orderURL domains"
      - "Verify URL generation works for various domains"
    ExpectedResult:
      - "Correctly generates address URLs for different domains"
  - CaseID: "U-LNK-B03"
    Module: "linkGenerator"
    Description: "Should handle special characters in token for address links"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "special-chars"
      - "url"
    Precondition:
      - "Token with special characters is provided"
    Steps:
      - "Call generateAddressLink with token containing special characters"
      - "Verify URL generation handles special characters"
    ExpectedResult:
      - "Correctly generates address URL with special characters in token"
