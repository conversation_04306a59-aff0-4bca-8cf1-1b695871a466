TestFile: whatsapp/services/messageBuilders.js
---
TestFunction: buildBasicTextMessageData
Cases:
  - CaseID: "U-MSG-A01"
    Module: "messageBuilders"
    Description: "Should build basic text message for notification"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "notification"
      - "text"
    Precondition:
      - "MessageBuilders instance is available"
      - "Valid recipientId and text provided"
    Steps:
      - "Call buildBasicTextMessageData with recipientId, text, 'notification'"
      - "Verify message structure"
    ExpectedResult:
      - "Returns valid notification message object"
      - "Message type is 'notification-messages'"
      - "Text content is correctly set"
  - CaseID: "U-MSG-A02"
    Module: "messageBuilders"
    Description: "Should build basic text message for dialog"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "dialog"
      - "text"
    Precondition:
      - "MessageBuilders instance is available"
      - "Valid dialogueId and text provided"
    Steps:
      - "Call buildBasicTextMessageData with dialogueId, text, 'dialog'"
      - "Verify message structure"
    ExpectedResult:
      - "Returns valid dialog message object"
      - "Message type is 'messages'"
      - "Text content is correctly set"
  - CaseID: "U-MSG-A03"
    Module: "messageBuilders"
    Description: "Should default to notification message type"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "default"
    Precondition:
      - "MessageBuilders instance is available"
      - "No message type specified"
    Steps:
      - "Call buildBasicTextMessageData without messageType parameter"
    ExpectedResult:
      - "Defaults to notification message type"
  - CaseID: "U-MSG-A04"
    Module: "messageBuilders"
    Description: "Should handle empty text message"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "empty-text"
    Precondition:
      - "MessageBuilders instance is available"
      - "Empty text string provided"
    Steps:
      - "Call buildBasicTextMessageData with empty text"
    ExpectedResult:
      - "Handles empty text gracefully"
      - "Message structure is still valid"
  - CaseID: "U-MSG-A05"
    Module: "messageBuilders"
    Description: "Should handle special characters in text"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "special-chars"
    Precondition:
      - "MessageBuilders instance is available"
      - "Text with special characters and emojis"
    Steps:
      - "Call buildBasicTextMessageData with special characters"
    ExpectedResult:
      - "Special characters are preserved correctly"
      - "Message structure remains valid"
---
TestFunction: buildQuickReplyMessageData
Cases:
  - CaseID: "U-MSG-B01"
    Module: "messageBuilders"
    Description: "Should build quick reply message with buttons"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "quick-reply"
      - "buttons"
    Precondition:
      - "MessageBuilders instance is available"
      - "Valid recipientId and options with buttons"
    Steps:
      - "Call buildQuickReplyMessageData with buttons array"
      - "Verify button mapping to context variables"
    ExpectedResult:
      - "Returns valid quick reply message object"
      - "Buttons are correctly mapped to qr1, qr2, etc."
      - "Payloads are correctly set"
  - CaseID: "U-MSG-B02"
    Module: "messageBuilders"
    Description: "Should handle single button"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "single-button"
    Precondition:
      - "MessageBuilders instance is available"
      - "Options with single button"
    Steps:
      - "Call buildQuickReplyMessageData with one button"
    ExpectedResult:
      - "Single button is correctly processed"
      - "qr1 and qr1_payload are set correctly"
  - CaseID: "U-MSG-B03"
    Module: "messageBuilders"
    Description: "Should handle header and footer"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "header-footer"
    Precondition:
      - "MessageBuilders instance is available"
      - "Options with header and footer"
    Steps:
      - "Call buildQuickReplyMessageData with header and footer"
    ExpectedResult:
      - "Header is set in var_header"
      - "Footer is set in var_footer"
  - CaseID: "U-MSG-B04"
    Module: "messageBuilders"
    Description: "Should throw error for invalid button count"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "MessageBuilders instance is available"
      - "Options with more than 3 buttons"
    Steps:
      - "Call buildQuickReplyMessageData with 4 buttons"
    ExpectedResult:
      - "Throws error: 'Invalid number of quick replies. Must be 1, 2, or 3.'"
---
TestFunction: buildWhatsAppTemplateMessageData
Cases:
  - CaseID: "U-MSG-C01"
    Module: "messageBuilders"
    Description: "Should build template message with variables"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "template"
      - "variables"
    Precondition:
      - "MessageBuilders instance is available"
      - "Valid template name and variables"
    Steps:
      - "Call buildWhatsAppTemplateMessageData with variables"
    ExpectedResult:
      - "Returns valid template message object"
      - "Template name is set correctly"
      - "Variables are mapped to context"
  - CaseID: "U-MSG-C02"
    Module: "messageBuilders"
    Description: "Should handle template with image"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "template"
      - "image"
    Precondition:
      - "MessageBuilders instance is available"
      - "Template options with image URL"
    Steps:
      - "Call buildWhatsAppTemplateMessageData with image"
    ExpectedResult:
      - "Image URL is set in context"
      - "Variables are correctly processed"
  - CaseID: "U-MSG-C03"
    Module: "messageBuilders"
    Description: "Should handle empty options"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "empty-options"
    Precondition:
      - "MessageBuilders instance is available"
      - "No options provided"
    Steps:
      - "Call buildWhatsAppTemplateMessageData without options"
    ExpectedResult:
      - "Template name is still set correctly"
      - "No errors occur with empty options"
---
TestFunction: buildDialogueMessageData
Cases:
  - CaseID: "U-MSG-D01"
    Module: "messageBuilders"
    Description: "Should build dialogue text message"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "dialogue"
      - "text"
    Precondition:
      - "MessageBuilders instance is available"
      - "Valid dialogueId and text"
    Steps:
      - "Call buildDialogueMessageData with dialogueId and text"
    ExpectedResult:
      - "Returns valid dialogue message object"
      - "Channel type is 'WHATSAPP'"
      - "Text content is correctly set"
  - CaseID: "U-MSG-D02"
    Module: "messageBuilders"
    Description: "Should handle empty text"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "boundary"
      - "empty-text"
    Precondition:
      - "MessageBuilders instance is available"
      - "Empty text string"
    Steps:
      - "Call buildDialogueMessageData with empty text"
    ExpectedResult:
      - "Handles empty text gracefully"
      - "Message structure is still valid"
---
TestFunction: Message Structure Validation
Cases:
  - CaseID: "U-MSG-E01"
    Module: "messageBuilders"
    Description: "Should create valid notification message structure"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "structure"
      - "notification"
    Precondition:
      - "MessageBuilders instance is available"
    Steps:
      - "Build notification message"
      - "Validate structure components"
    ExpectedResult:
      - "All required structure components are present"
      - "Type, attributes, relationships are defined"
  - CaseID: "U-MSG-E02"
    Module: "messageBuilders"
    Description: "Should create valid dialogue message structure"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "structure"
      - "dialogue"
    Precondition:
      - "MessageBuilders instance is available"
    Steps:
      - "Build dialogue message"
      - "Validate structure components"
    ExpectedResult:
      - "All required structure components are present"
      - "Data type, attributes, relationships are defined"
---
TestFunction: buildItemAddedMessage
Cases:
  - CaseID: "U-SVC-MB-301"
    Module: "messageBuilders"
    Description: "Should build a confirmation message after adding an item"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "confirmation"
    Precondition:
      - "An `orderItem` object is provided with details of the item just added."
    Steps:
      - "1. Call `buildItemAddedMessage(orderItem)`."
    ExpectedResult:
      - "Returns a valid WhatsApp text or interactive message object."
      - "The message text confirms that the item was added."
      - "It may include buttons to 'View Cart' or 'Continue Shopping'."
---
TestFunction: buildOrderConfirmationMessage
Cases:
  - CaseID: "U-SVC-MB-401"
    Module: "messageBuilders"
    Description: "Should build a final order confirmation message"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "message-building"
      - "confirmation"
      - "order"
    Precondition:
      - "An `order` object is provided with final details (order number, items, total price, estimated delivery time)."
    Steps:
      - "1. Call `buildOrderConfirmationMessage(order)`."
    ExpectedResult:
      - "Returns a valid WhatsApp text message object."
      - "The message text contains all the relevant order details in a clear, readable format."
