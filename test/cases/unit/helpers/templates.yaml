TestFile: helpers/templates.js
---
TestFunction: getTemplate
Cases:
  - CaseID: "U-HLP-TPL-101"
    Module: "templatesHelper"
    Description: "Should render an EJS template successfully with provided data"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "ejs"
      - "templating"
      - "filesystem"
    Precondition:
      - "A `templateName` and a `data` object are provided."
      - "Mock `ejs.renderFile` to resolve with a predefined HTML string."
      - "Mock `path.join` to verify the correct template path is constructed."
    Steps:
      - "1. Call `getTemplate` with the template name and data."
    ExpectedResult:
      - "`ejs.renderFile` is called with the correct file path and data object."
      - "The function's returned promise resolves with the mock HTML string."

  - CaseID: "U-HLP-TPL-102"
    Module: "templatesHelper"
    Description: "Should reject the promise if template rendering fails"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `ejs.renderFile` to call its callback with an error (e.g., `new Error('Template not found')`)."
    Steps:
      - "1. Call `getTemplate`."
      - "2. Assert that the returned promise is rejected."
    ExpectedResult:
      - "The promise is rejected with the error from `ejs.renderFile`."
