TestFile: helpers/populate-countries-data.js
---
TestFunction: Data Population Script
Cases:
  - CaseID: "U-HLP-POP-101"
    Module: "populateCountriesScript"
    Description: "Should read country data from JSON and update the database"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "script"
      - "seeding"
      - "database"
      - "filesystem"
    Precondition:
      - "Mock `fs.readFileSync` to return a JSON string with a few country objects."
      - "Mock `mongoose.connect` to resolve successfully."
      - "Mock `Country.findOneAndUpdate` to resolve successfully."
      - "Mock `mongoose.disconnect`."
    Steps:
      - "1. Run the script."
    ExpectedResult:
      - "`fs.readFileSync` is called with the correct path."
      - "`mongoose.connect` is called."
      - "`Country.findOneAndUpdate` is called for each country in the mock JSON data."
      - "`mongoose.disconnect` is called at the end."
      - "Success messages are logged."

  - CaseID: "U-HLP-POP-102"
    Module: "populateCountriesScript"
    Description: "Should handle errors during file reading"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "filesystem"
    Precondition:
      - "Mock `fs.readFileSync` to throw an error."
      - "Mock the logger to spy on error logs."
    Steps:
      - "1. Run the script."
    ExpectedResult:
      - "The file system error is caught and logged."
      - "The script exits without attempting to connect to the database."

  - CaseID: "U-HLP-POP-103"
    Module: "populateCountriesScript"
    Description: "Should handle errors during database update"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "database"
    Precondition:
      - "Mock `fs.readFileSync` to return valid JSON."
      - "Mock `mongoose.connect`."
      - "Mock `Country.findOneAndUpdate` to reject with an error."
      - "Mock the logger."
    Steps:
      - "1. Run the script."
    ExpectedResult:
      - "The database error is caught and logged."
      - "`mongoose.disconnect` is still called."
