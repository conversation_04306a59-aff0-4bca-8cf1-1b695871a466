TestFile: helpers/api.js
---
TestFunction: getRequest
Cases:
  - CaseID: "U-HLP-API-101"
    Module: "apiHelper"
    Description: "Should return data from a successful GET request"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "axios"
      - "api"
    Precondition:
      - "Mock `axios.get` to resolve successfully with a predefined data object (e.g., `{ data: { success: true } }`)."
    Steps:
      - "1. Call `getRequest` with a sample URL."
    ExpectedResult:
      - "`axios.get` is called once with the provided URL."
      - "The function returns the `data` object from the axios response (e.g., `{ success: true }`)."

  - CaseID: "U-HLP-API-102"
    Module: "apiHelper"
    Description: "Should log and re-throw an error when the GET request fails"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
    Precondition:
      - "Mock `axios.get` to reject with a new `Error('Network Error')`."
      - "Mock the logger to spy on the error logging."
    Steps:
      - "1. Call `getRequest` with a sample URL."
      - "2. <PERSON><PERSON><PERSON> that the function call throws an error."
    ExpectedResult:
      - "The error message ('Network Error') is logged."
      - "The original error is re-thrown and can be caught by the caller."
