TestFile: whatsapp/routes/index.js
---
TestFunction: Router Configuration
Cases:
  - CaseID: "U-RTE-IDX-101"
    Module: "whatsappRoutes"
    Description: "Should correctly configure the GET /webhook route for verification"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "routing"
      - "configuration"
    Precondition:
      - "The Express router is initialized."
      - "The `webhookController` is imported."
    Steps:
      - "1. Inspect the router's stack for the GET method on the '/webhook' path."
    ExpectedResult:
      - "A route exists for GET /webhook."
      - "The handler for this route is `webhookController.verifyWebhook`."
  - CaseID: "U-RTE-IDX-102"
    Module: "whatsappRoutes"
    Description: "Should correctly configure the POST /webhook route with middleware"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "routing"
      - "middleware"
      - "configuration"
    Precondition:
      - "The Express router is initialized."
      - "The `validateWhatsappSignature` middleware is imported."
      - "The `webhookController` is imported."
    Steps:
      - "1. Inspect the router's stack for the POST method on the '/webhook' path."
    ExpectedResult:
      - "A route exists for POST /webhook."
      - "The route handlers are an array containing `validateWhatsappSignature` followed by `webhookController.processIncomingMessage`."
