<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态测试用例查看器</title>
    <script src="https://cdn.jsdelivr.net/npm/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; display: flex; margin: 0; height: 100vh; background-color: #fee7d5; }
        nav { width: 250px; border-right: 1px solid #dcdfe6; padding: 1em; overflow-y: auto; background: #ffffff; flex-shrink: 0; }
        nav h3 { margin-top: 0; }
        nav a { display: block; padding: 0.2em 1em; text-decoration: none; color: #303133; border-radius: 6px; margin-bottom: 5px; font-weight: 500;}
        nav a:hover { background-color: #ecf5ff; color: #409eff; }
        nav a.active { background-color: #409eff; color: white; }
        main { flex-grow: 1; padding: 2em; overflow: auto; }
        #content-container h2 { margin-top: 0; color: #303133; border-bottom: 2px solid #f8ceae; padding-bottom: 0.5em; margin-bottom: 1em;}

        table { border-collapse: collapse; width: 100%; font-size: 14px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); border-radius: 8px; overflow: hidden; margin-top: 1em; }
        th, td { border: 1px solid #dcdfe6; padding: 4px 3px; text-align: left; vertical-align: top; }
        th { background-color: #f8ceae; color: #606266; font-weight: 600; text-transform: capitalize; }
        /* 恢复原始的行颜色 */
        tr:nth-child(even) { background-color: #f8ceae; }

        ul { margin: 0; padding-left: 10px; }
        hr { border: 0; border-top: 1px solid #f8ceae; margin: 2.5em 0; }

        /* 状态标签样式 (来自原始文件) */
        .status-implemented { background: #d4edda; color: #155724; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        .status-not-implemented, .status-planned { background: #f8d7da; color: #721c24; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        .status-inprogress, .status-in-progress { background: #fff3cd; color: #856404; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        
        /* 优先级标签样式 (来自原始文件) */
        .importance-high { background: #ff4757; color: white; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        .importance-medium { background: #ffa502; color: white; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        .importance-low { background: #2ed573; color: white; padding: 2px 4px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }
        .tag { background: #e3f2fd; color: #1976d2; padding: 2px 4px; border-radius: 3px; font-size: 0.85em; margin-right: 4px; white-space: nowrap; display: inline-block; }

        /* --- START OF NEW STYLES --- */
        /* 优化特定列的宽度，确保内容较多的列有足够空间 */
        th[data-col-name="Description"],
        th[data-col-name="Precondition"],
        th[data-col-name="Steps"],
        th[data-col-name="ExpectedResult"] {
            min-width: 200px; /* 为这些列设置一个最小宽度 */
        }
        /* --- END OF NEW STYLES --- */
    </style>
</head>
<body>

    <nav id="file-list">
        <h3>测试用例文件</h3>
    </nav>

    <main id="content-container">
        <h2>请选择一个测试用例文件进行查看</h2>
    </main>

    <script>
        const fileListNav = document.getElementById('file-list');
        const contentContainer = document.getElementById('content-container');

        // 格式化特殊字段以应用样式
        function formatSpecialField(key, value) {
            const lowerKey = key.toLowerCase();
            if (lowerKey === 'status') {
                const statusClass = `status-${String(value).toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
                return `<span class="${statusClass}">${value}</span>`;
            }
            if (lowerKey === 'importance' || lowerKey === 'priority') {
                const importanceClass = `importance-${(String(value) || '').toLowerCase()}`;
                return `<span class="${importanceClass}">${value}</span>`;
            }
            if (lowerKey === 'tags' && Array.isArray(value)) {
                return value.map(tag => `<span class="tag">${tag}</span>`).join(' ');
            }
            return null;
        }

        // 渲染文件内容
        function renderFileContent(yamlContent, fileName) {
            let contentHTML = `<h2>${fileName}</h2>`;
            try {
                const documents = jsyaml.loadAll(yamlContent);

                documents.forEach((doc, index) => {
                    if (index > 0) {
                        contentHTML += '<hr>';
                    }
                    if (!doc) return;

                    const possibleTestCaseKeys = ['Cases', 'test_cases', 'testCases', 'cases', 'tests'];
                    let testCases = null;
                    let testCaseKey = null;

                    for (const key of possibleTestCaseKeys) {
                        if (doc[key] && Array.isArray(doc[key])) {
                            testCases = doc[key];
                            testCaseKey = key;
                            break;
                        }
                    }

                    const headerData = { ...doc };
                    if (testCaseKey) {
                        delete headerData[testCaseKey];
                    }
                    
                    if (Object.keys(headerData).length > 0) {
                        const yamlString = jsyaml.dump(headerData);
                        contentHTML += yamlString.replace(/</g, '<').replace(/>/g, '>');
                    }

                    if (testCases && testCases.length > 0) {
                        const headersSet = new Set();
                        testCases.forEach(tc => Object.keys(tc).forEach(key => headersSet.add(key)));
                        const headers = Array.from(headersSet);

                        // --- MODIFICATION START ---
                        // 添加 data-col-name 属性到 th 标签，用于CSS选择
                        let tableHTML = `<table><thead><tr>${headers.map(h => `<th data-col-name="${h}">${h}</th>`).join('')}</tr></thead><tbody>`;
                        // --- MODIFICATION END ---
                        
                        testCases.forEach(tc => {
                            tableHTML += '<tr>';
                            headers.forEach(headerKey => {
                                const value = tc[headerKey];
                                let displayValue = '';
                                if (value !== undefined && value !== null) {
                                    const specialFormatted = formatSpecialField(headerKey, value);
                                    if (specialFormatted) {
                                        displayValue = specialFormatted;
                                    } else if (Array.isArray(value)) {
                                        displayValue = `<ul>${value.map(item => `<li>${String(item).replace(/</g, '<')}</li>`).join('')}</ul>`;
                                    } else if (typeof value === 'object') {
                                        displayValue = `${JSON.stringify(value, null, 2).replace(/</g, '<')}`;
                                    } else {
                                        displayValue = String(value).replace(/</g, '<').replace(/>/g, '>').replace(/\n/g, '<br>');
                                    }
                                }
                                tableHTML += `<td>${displayValue}</td>`;
                            });
                            tableHTML += '</tr>';
                        });
                        tableHTML += '</tbody></table>';
                        contentHTML += tableHTML;
                    }
                });
            } catch (error) {
                console.error('YAML解析或渲染错误:', error);
                contentHTML += `<p style="color: red;">文件内容解析失败: ${error.message}</p><pre>${error.stack}</pre>`;
            }
            
            contentContainer.innerHTML = contentHTML;
        }

        // 加载并处理单个文件
        async function loadAndRenderFile(filePath) {
            try {
                const response = await fetch(filePath);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const fileContent = await response.text();
                const fileName = filePath.split('/').pop();
                
                contentContainer.innerHTML = `<p>正在加载 ${fileName}...</p>`;
                
                setTimeout(() => {
                    renderFileContent(fileContent, fileName);
                }, 10);
            } catch (error) {
                console.error('加载或解析文件失败:', error);
                contentContainer.innerHTML = `<h2>加载文件 ${filePath} 失败</h2><p style="color: red;">${error.message}</p>`;
            }
        }

        // 初始化
        async function initialize() {
            try {
                const response = await fetch('_manifest.json');
                if (!response.ok) throw new Error('无法加载清单文件 _manifest.json');
                const filePaths = await response.json();

                filePaths.forEach((filePath, index) => {
                    const link = document.createElement('a');
                    link.href = '#';
                    link.textContent = filePath;//filePath.split('/').pop();
                    link.dataset.path = filePath;

                    link.onclick = (e) => {
                        e.preventDefault();
                        document.querySelectorAll('#file-list a').forEach(a => a.classList.remove('active'));
                        link.classList.add('active');
                        loadAndRenderFile(filePath);
                    };
                    fileListNav.appendChild(link);

                    if (index === 0) {
                        link.classList.add('active');
                        loadAndRenderFile(filePath);
                    }
                });
            } catch(error) {
                console.error('初始化失败:', error);
                contentContainer.innerHTML = `<h2 style="color: red;">初始化失败: ${error.message}</h2>`;
            }
        }

        initialize();
    </script>
</body>
</html>