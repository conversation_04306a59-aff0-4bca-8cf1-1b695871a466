Scenario: Complete Refund Business Flow E2E Tests
---
# 完整退款业务流程端到端测试
TestFunction: completeRefundWorkflow
Cases:
  - CaseID: "E-REF-A01"
    Module: "refund-e2e"
    Description: "完整的订单取消和全额退款端到端流程"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "e2e"
      - "full-refund"
      - "critical-path"
      - "business-flow"
    Precondition:
      - "启动完整应用程序栈"
      - "真实MongoDB和Redis容器运行"
      - "Stripe测试环境配置"
      - "WhatsApp webhook配置"
      - "通知系统配置"
    Steps:
      - "客户通过WhatsApp下单"
      - "餐厅确认订单"
      - "客户完成Stripe支付"
      - "订单状态变为PAID"
      - "餐厅因缺货申请取消订单"
      - "系统调用Stripe退款API"
      - "Stripe webhook回调更新状态"
      - "系统发送退款通知给客户"
      - "客户收到WhatsApp退款通知"
    ExpectedResult:
      - "完整业务流程无错误执行"
      - "订单状态正确变更: PENDING → PAID → CANCELLED"
      - "退款状态正确变更: NONE → FULL"
      - "Stripe退款成功处理"
      - "客户收到准确的退款通知"
      - "所有系统组件协同工作"
  - CaseID: "E-REF-A02"
    Module: "refund-e2e"
    Description: "部分退款端到端业务流程"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "e2e"
      - "partial-refund"
      - "business-flow"
    Precondition:
      - "完整应用程序栈运行"
      - "已完成支付的订单存在"
      - "餐厅管理员已登录"
    Steps:
      - "餐厅管理员登录管理系统"
      - "查看已支付订单列表"
      - "选择需要部分退款的订单"
      - "输入退款金额和原因"
      - "提交部分退款申请"
      - "系统验证退款金额有效性"
      - "系统调用Stripe部分退款API"
      - "Stripe处理退款并回调"
      - "系统更新订单和退款状态"
      - "系统发送部分退款通知"
      - "客户收到部分退款到账通知"
    ExpectedResult:
      - "部分退款流程完整执行"
      - "订单状态变为PARTIALLY_REFUNDED"
      - "退款状态变为PARTIAL"
      - "退款金额正确计算和处理"
      - "客户收到准确的部分退款通知"
      - "剩余订单金额可继续退款"

  - CaseID: "E-REF-A03"
    Module: "refund-e2e"
    Description: "多次部分退款端到端流程"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "e2e"
      - "multiple-refunds"
      - "complex-flow"
    Precondition:
      - "完整应用程序栈运行"
      - "已支付订单（金额100.00）"
      - "餐厅管理员权限"
    Steps:
      - "执行第一次部分退款（30.00）"
      - "验证订单状态变为PARTIALLY_REFUNDED"
      - "验证退款状态变为PARTIAL"
      - "客户收到第一次退款通知"
      - "执行第二次部分退款（20.00）"
      - "验证累计退款金额为50.00"
      - "验证退款状态保持PARTIAL"
      - "客户收到第二次退款通知"
      - "执行最后一次退款（50.00）"
      - "验证退款状态变为FULL"
      - "验证订单状态变为CANCELLED"
      - "客户收到最终退款通知"
    ExpectedResult:
      - "多次退款流程完整执行"
      - "每次退款都正确处理"
      - "状态变更逻辑正确"
      - "累计金额计算准确"
      - "客户收到所有退款通知"
      - "最终达到全额退款状态"
---
# 退款错误处理和异常场景端到端测试
TestFunction: refundErrorHandlingE2E
Cases:
  - CaseID: "E-REF-B01"
    Module: "refund-e2e"
    Description: "退款失败场景端到端处理"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "e2e"
      - "error-handling"
      - "failure-scenarios"
    Precondition:
      - "完整应用程序栈运行"
      - "已支付订单存在"
      - "Mock Stripe API失败响应"
    Steps:
      - "餐厅管理员发起退款申请"
      - "系统调用Stripe退款API"
      - "Stripe API返回失败响应"
      - "系统处理API失败"
      - "系统更新退款状态为FAILED"
      - "系统发送退款失败通知"
      - "管理员收到失败通知"
      - "客户收到退款处理失败通知"
      - "系统记录详细错误日志"
    ExpectedResult:
      - "退款失败被正确处理"
      - "退款状态正确设为FAILED"
      - "订单状态保持不变"
      - "错误信息被正确记录"
      - "相关方收到失败通知"
      - "系统保持数据一致性"

  - CaseID: "E-REF-B02"
    Module: "refund-e2e"
    Description: "超额退款阻止端到端测试"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "e2e"
      - "validation"
      - "business-rules"
    Precondition:
      - "完整应用程序栈运行"
      - "已支付订单（金额100.00）"
      - "餐厅管理员已登录"
    Steps:
      - "管理员尝试退款150.00（超过订单金额）"
      - "系统验证退款金额"
      - "系统拒绝超额退款请求"
      - "系统返回明确的错误信息"
      - "系统不调用Stripe API"
      - "系统不创建退款记录"
      - "管理员收到错误提示"
    ExpectedResult:
      - "超额退款被正确阻止"
      - "错误信息清晰明确"
      - "订单状态保持不变"
      - "无无效的退款记录"
      - "系统保持数据完整性"
---
# 权限和安全端到端测试
TestFunction: securityAndAuthorizationE2E
Cases:
  - CaseID: "E-SEC-A01"
    Module: "security-e2e"
    Description: "跨餐厅退款权限验证端到端测试"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "e2e"
      - "security"
      - "authorization"
      - "cross-tenant"
    Precondition:
      - "完整应用程序栈运行"
      - "餐厅A的已支付订单"
      - "餐厅B的管理员账户"
    Steps:
      - "餐厅B管理员登录系统"
      - "尝试访问餐厅A的订单"
      - "尝试对餐厅A的订单发起退款"
      - "系统验证跨餐厅权限"
      - "系统拒绝跨餐厅操作"
      - "系统返回权限错误"
      - "系统记录安全日志"
    ExpectedResult:
      - "跨餐厅操作被正确阻止"
      - "返回403权限错误"
      - "订单状态保持不变"
      - "无跨餐厅退款记录"
      - "安全事件被正确记录"

  - CaseID: "E-SEC-A02"
    Module: "security-e2e"
    Description: "未认证用户退款访问端到端测试"
    Importance: "Critical"
    Status: "Planned"
    Tags:
      - "e2e"
      - "security"
      - "authentication"
    Precondition:
      - "完整应用程序栈运行"
      - "已支付订单存在"
      - "无认证token"
    Steps:
      - "未认证用户尝试访问退款接口"
      - "系统验证认证状态"
      - "系统拒绝未认证访问"
      - "系统返回认证错误"
      - "系统记录安全事件"
    ExpectedResult:
      - "未认证访问被正确阻止"
      - "返回401认证错误"
      - "无退款操作执行"
      - "安全事件被记录"

---
# 数据一致性和并发端到端测试
TestFunction: dataConsistencyE2E
Cases:
  - CaseID: "E-DAT-A01"
    Module: "data-e2e"
    Description: "多次退款数据一致性端到端验证"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "e2e"
      - "data-consistency"
      - "multiple-operations"
    Precondition:
      - "完整应用程序栈运行"
      - "已支付订单（金额100.00）"
      - "数据库事务支持"
    Steps:
      - "执行第一次部分退款30.00"
      - "验证数据库中订单totalRefunded=30.00"
      - "验证refundStatus='PARTIAL'"
      - "执行第二次部分退款20.00"
      - "验证数据库中订单totalRefunded=50.00"
      - "验证refundStatus保持'PARTIAL'"
      - "通过不同接口查询验证数据一致性"
      - "验证退款记录总和与订单totalRefunded一致"
    ExpectedResult:
      - "所有数据库查询返回一致结果"
      - "退款记录总和=订单totalRefunded"
      - "refundStatus与实际退款金额匹配"
      - "数据库关联关系正确"
      - "无数据不一致现象"
---
# E2E 测试总结
TestSummary:
  TotalCases: 8
  CriticalCases: 3
  HighPriorityCases: 5
  PlannedCases: 8
  ImplementedCases: 0

  CoverageAreas:
    - "完整退款业务流程"
    - "部分退款业务流程"
    - "多次退款处理"
    - "错误处理和异常场景"
    - "安全和权限验证"
    - "数据一致性验证"

  TestEnvironment:
    - "完整应用程序栈"
    - "真实MongoDB数据库"
    - "真实Redis缓存"
    - "Stripe测试API"
    - "WhatsApp webhook模拟"
    - "通知系统集成"
    - "完整业务流程环境"

  BusinessFlows:
    - "客户下单 → 支付 → 退款 → 通知"
    - "餐厅管理 → 退款申请 → 处理 → 状态更新"
    - "支付网关 → 退款处理 → 回调 → 状态同步"
    - "权限验证 → 业务规则 → 数据一致性"
    - "GraphQL Schema验证"
