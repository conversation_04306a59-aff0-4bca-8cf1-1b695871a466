TestFile: basic.test.js
---
TestFunction: Application Health
Cases:
  - CaseID: "E-APP-A01"
    Module: "application"
    Description: "Should respond to health check"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "health-check"
      - "basic"
    Precondition:
      - "Test application is running"
      - "Database connection established"
    Steps:
      - "Send GET request to /health endpoint"
      - "Verify response status is 200"
      - "Check response body structure"
    ExpectedResult:
      - "Response status is 200"
      - "Response body contains status: 'ok'"
      - "Response body contains timestamp"
  - CaseID: "E-APP-A02"
    Module: "application"
    Description: "Should have working GraphQL endpoint"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "graphql"
      - "introspection"
    Precondition:
      - "GraphQL server is running"
      - "Schema is loaded"
    Steps:
      - "Send introspection query to /graphql"
      - "Verify GraphQL schema response"
      - "Check queryType name"
    ExpectedResult:
      - "Response status is 200"
      - "Schema data is defined"
      - "QueryType name is 'Query'"
  - CaseID: "E-APP-A03"
    Module: "application"
    Description: "Should have WhatsApp endpoints"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "whatsapp"
      - "endpoints"
    Precondition:
      - "WhatsApp webhook endpoint configured"
    Steps:
      - "Send GET request to /whatsapp/webhook"
      - "Check response status"
      - "Verify endpoint exists"
    ExpectedResult:
      - "Response status is 200, 405, or 404"
      - "Endpoint is accessible"
---
TestFunction: Database Operations
Cases:
  - CaseID: "E-DB-A01"
    Module: "database"
    Description: "Should be able to create and query customer directly"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "database"
      - "customer"
    Precondition:
      - "MongoDB connection established"
      - "Customer model available"
      - "Test database cleared"
    Steps:
      - "Create customer with valid data"
      - "Save customer to database"
      - "Query customer by phone"
      - "Verify customer data"
    ExpectedResult:
      - "Customer is created successfully"
      - "Customer can be found by phone"
      - "Customer data matches input"
      - "Customer has one address"
  - CaseID: "E-DB-A02"
    Module: "database"
    Description: "Should be able to create restaurant owner directly"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "database"
      - "owner"
    Precondition:
      - "MongoDB connection established"
      - "Owner model available"
      - "Test database cleared"
    Steps:
      - "Create owner with valid data"
      - "Save owner to database"
      - "Query owner by email"
      - "Verify owner data"
    ExpectedResult:
      - "Owner is created successfully"
      - "Owner can be found by email"
      - "Owner data matches input"
---
TestFunction: GraphQL Schema Validation
Cases:
  - CaseID: "E-GQL-A01"
    Module: "graphql"
    Description: "Should have required types in schema"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "e2e"
      - "graphql"
      - "schema"
      - "types"
    Precondition:
      - "GraphQL server running"
      - "Schema loaded"
    Steps:
      - "Query all types in schema"
      - "Check for basic types"
      - "Verify type count"
    ExpectedResult:
      - "Schema contains String type"
      - "Schema contains ID type"
      - "Schema contains Boolean type"
      - "Type count is reasonable"
