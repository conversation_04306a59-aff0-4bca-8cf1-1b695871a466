const fs = require('fs');
const path = require('path');

// Configuration
const DOCS_DIR = __dirname;
const MANIFEST_PATH = path.join(DOCS_DIR, '_manifest.json');

// Find all YAML files recursively
function findYamlFiles(dir, baseDir = dir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  let yamlFiles = [];

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    const relativePath = path.relative(baseDir, fullPath);
    
    if (entry.isDirectory()) {
      yamlFiles = yamlFiles.concat(findYamlFiles(fullPath, baseDir));
    } else if (entry.isFile() && /\.ya?ml$/i.test(entry.name)) {
      // Convert Windows path separators to forward slashes for consistency
      yamlFiles.push(relativePath.replace(/\\/g, '/'));
    }
  }

  return yamlFiles;
}

// Update the manifest file
function updateManifest() {
  try {
    const yamlFiles = findYamlFiles(DOCS_DIR);
    
    // Sort files alphabetically for consistency
    yamlFiles.sort();
    
    // Write to manifest file
    fs.writeFileSync(
      MANIFEST_PATH,
      JSON.stringify(yamlFiles, null, 2) + '\n',
      'utf8'
    );
    
    console.log(`✅ Manifest updated with ${yamlFiles.length} YAML files`);
    return 0;
  } catch (error) {
    console.error('❌ Error updating manifest:', error);
    return 1;
  }
}

// Run the update
if (require.main === module) {
  process.exit(updateManifest());
}

module.exports = { updateManifest };
