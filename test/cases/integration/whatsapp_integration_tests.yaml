Feature: WhatsApp Webhook Integration

Whatsapp_integration_tests.yaml:

Whatsapp消息:
1. welcome消息
  a. brand介绍
  b. 已选restaurant介绍 + 订餐链接
  c. 餐厅选择按钮
  d. 历史订单按钮
2. 隐私确认消息
3. 餐厅选择消息
4. 历史订单消息
5. 已完成订单详细信息
7. 订单待确认信息
8. 订单付款信息

whatsappMessageReception:
- when received a message:
	- 如果该手机号尚未注册, 发送隐私确认消息
	- 如果该手机号已经注册但是没有redis session信息(本条消息之前, 过去24小时无whatsapp消息), 创建session, 并发送welcome消息
	- 如果该手机号已经注册也有session(本条消息之前, 过去24小时有whatsapp消息),获取session, 并发送welcome消息

Welcome消息发送:
- 发送welcome消息时
	- 如果当前brand只有一家restaurant, welcome消息包含 消息1.a + 1.b
	- 如果当前brand有多家restaurant, 当前restaurant已经被选择, 1.a + 1.b + 1.c
	- 如果当前brand有多家restaurant, 无restaurant被选择, 1.a + 1.c
  - 如果该用户有该brand的历史订单, 添加1.d在之前的消息上.
  - 如果有未付款订单, 且该订单未超过1个小时, 发送付款提醒. 如果该订单已经超过1小时, 取消该订单, 并发送订单取消消息
选择restaurant:
- 如果当前brand只有一家restaurant, 该restaurant在创建session时就应该被选择
- 如果当前brand有多家restaurant, 用户之前有订单, 那么用户的前一个订单的餐厅在session创建时自动被认为是被选择的restaurant
- 如果当前brand有多家restaurant, 用户之前无订单, 那么session创建时, 认为没有任何restaurant被选择
- 如果收到restaurant selection消息, session中的restaurant应该被更新

接收到订单信息:
- 验证订单数据是否有效, 有效的情况下, 发送order confirm消息
- 验证订单数据是否有效, 无效的情况下, 发送消息提示出错, 并发送订餐链接, 请用户重新开始
- 如果当前有未付款订单, 提示当前有未付款订单, 请付款(并含付款链接)或者进入未付款订单管理, 删除未付款订单

接收到订单confirm:
- 如果是旧的未confirm的订单(订单confirm ID已经无效), 提醒客户这似乎是个旧购物车, 已经被修改, 请重新确认购物车(发送订餐链接)
- 如果是当前的购物车的confirm ID, 发送付款相关信息.

接收到付款成功event:
- 该order应该被加入order数据库, 并且推送到restaurant app端.
- 如果不成功呢? 可能情况是什么?

接收到历史订单查询消息:
- 发送当前用户当前brand的历史订单列表(最近10个, 不限restaurnat), 如果还有更多历史订单, 附加一个more的按钮
- 如果历史订单数量不够10个, 就发送所有历史订单

接收到历史订单的具体查询:
- 发送历史订单的详细信息

接收到reorder 消息:
- 将order里面的的内容重新加入购物车,  将restaurant选择为reorder的order的餐厅, 然后发送消息含订购链接,
	- 如果有东西缺货或者不再售卖, 应该在消息中列出 xxx, xxx 当前不售卖.




---
# WhatsApp消息接收测试组
TestFunction: whatsappMessageReception
Cases:
  - CaseID: "I-WA-A01"
    Module: "whatsapp"
    Description: "Should process incoming message and create session"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "webhook"
      - "message-reception"
      - "session-creation"
    Precondition:
      - "Test database is connected"
      - "Redis is available"
      - "Test user exists with phone +1234567890"
      - "WhatsApp service is mocked"
    Steps:
      - "Create webhook payload with text message 'Hello'"
      - "Generate valid XMMC signature"
      - "Send webhook request"
      - "Wait for async processing"
      - "Verify response and session creation"
    ExpectedResult:
      - "Response status is 202"
      - "Response message is 'Webhook received'"
      - "Session is created in Redis"
      - "WhatsApp service processes message"

  - CaseID: "I-WA-A02"
    Module: "whatsapp"
    Description: "Should handle interactive message reception"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "webhook"
      - "interactive-message"
    Precondition:
      - "Test database is connected"
      - "User session exists"
      - "WhatsApp service is mocked"
    Steps:
      - "Create webhook payload with interactive message"
      - "Set button reply payload"
      - "Generate valid signature"
      - "Send webhook request"
      - "Verify interactive message processing"
    ExpectedResult:
      - "Response status is 202"
      - "Interactive message is processed"
      - "Button payload is extracted"
      - "Appropriate action is triggered"

---
# WhatsApp会话管理测试组
TestFunction: whatsappSessionManagement
Cases:
  - CaseID: "I-WA-B01"
    Module: "whatsapp"
    Description: "Should create new session for new user"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "session"
      - "new-user"
    Precondition:
      - "Redis is available"
      - "User has never interacted before"
      - "WhatsApp service is mocked"
    Steps:
      - "Send message from new phone number"
      - "Process webhook event"
      - "Verify session creation"
      - "Check session data"
    ExpectedResult:
      - "New session record is created in Redis"
      - "Session is associated with phone number"
      - "OrderFSM is initialized for session"
      - "Session contains correct metadata"

  - CaseID: "I-WA-B02"
    Module: "whatsapp"
    Description: "Should retrieve existing session for returning user"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "session"
      - "returning-user"
    Precondition:
      - "Redis is available"
      - "User session already exists"
      - "Session is not expired"
    Steps:
      - "Send message from existing phone number"
      - "Process webhook event"
      - "Verify session retrieval"
      - "Check session continuity"
    ExpectedResult:
      - "Existing session is retrieved"
      - "Session data is preserved"
      - "Context is maintained"
      - "No duplicate sessions created"

---
# WhatsApp状态机测试组
TestFunction: whatsappStateMachine
Cases:
  - CaseID: "I-WA-C01"
    Module: "whatsapp"
    Description: "Should handle valid state transition"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "state-machine"
      - "transition"
    Precondition:
      - "Session is in 'welcomed' state"
      - "Valid command is available"
      - "FSM is initialized"
    Steps:
      - "Send text message with valid command 'order'"
      - "Process message through FSM"
      - "Verify state transition"
      - "Check new state"
    ExpectedResult:
      - "FSM transitions to 'ordering' state"
      - "State change is persisted"
      - "Appropriate response is sent"
      - "Context is updated"

  - CaseID: "I-WA-C02"
    Module: "whatsapp"
    Description: "Should handle invalid input without state transition"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "state-machine"
      - "invalid-input"
    Precondition:
      - "Session is in 'ordering' state"
      - "Invalid input is prepared"
    Steps:
      - "Send random unexpected text message"
      - "Process message through FSM"
      - "Verify state remains unchanged"
      - "Check error handling"
    ExpectedResult:
      - "FSM remains in 'ordering' state"
      - "Error or help message is sent"
      - "Session state is preserved"
      - "User is guided appropriately"

---
# WhatsApp错误处理测试组
TestFunction: whatsappErrorHandling
Cases:
  - CaseID: "I-WA-D01"
    Module: "whatsapp"
    Description: "Should reject webhook with invalid signature"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "negative"
      - "security"
      - "signature-validation"
    Precondition:
      - "Webhook signature validation is enabled"
      - "Invalid signature is prepared"
    Steps:
      - "Create webhook payload"
      - "Generate invalid signature"
      - "Send webhook request"
      - "Verify rejection"
    ExpectedResult:
      - "Response status is 401"
      - "Request is not processed"
      - "WhatsApp service is not called"
      - "Security is maintained"

  - CaseID: "I-WA-D02"
    Module: "whatsapp"
    Description: "Should handle malformed webhook payload"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "negative"
      - "error-handling"
      - "malformed-payload"
    Precondition:
      - "Webhook endpoint is available"
      - "Malformed payload is prepared"
    Steps:
      - "Create payload missing 'included' array"
      - "Generate valid signature"
      - "Send webhook request"
      - "Verify error handling"
    ExpectedResult:
      - "Response status is 400, 401, or 202"
      - "Error is handled gracefully"
      - "WhatsApp service is not called"
      - "Error is logged appropriately"

  - CaseID: "I-WA-D03"
    Module: "whatsapp"
    Description: "Should handle unknown message type"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "error-handling"
      - "unknown-type"
    Precondition:
      - "Webhook endpoint is available"
      - "Unknown message type is prepared"
    Steps:
      - "Send webhook with unsupported message type"
      - "Process webhook event"
      - "Verify graceful handling"
      - "Check logging"
    ExpectedResult:
      - "Response status is 200 (WhatsApp requirement)"
      - "Warning is logged"
      - "Message is gracefully ignored"
      - "No errors are thrown"

---
# WhatsApp安全测试组
TestFunction: whatsappSecurity
Cases:
  - CaseID: "I-WA-E01"
    Module: "whatsapp"
    Description: "Should accept request with valid signature"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "security"
      - "signature-validation"
    Precondition:
      - "Webhook signature validation is enabled"
      - "Valid signature secret is configured"
    Steps:
      - "Create webhook payload"
      - "Generate correct X-Hub-Signature-256 header"
      - "Send webhook request"
      - "Verify acceptance"
    ExpectedResult:
      - "Response status is 200"
      - "Request is processed normally"
      - "Signature validation passes"
      - "Webhook processing continues"

  - CaseID: "I-WA-E02"
    Module: "whatsapp"
    Description: "Should validate XMMC signature correctly"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "positive"
      - "security"
      - "xmmc-signature"
    Precondition:
      - "XMMC signature validation is enabled"
      - "Webhook secret is configured"
    Steps:
      - "Create webhook payload"
      - "Generate XMMC signature with correct secret"
      - "Send webhook request with signature"
      - "Verify signature validation"
    ExpectedResult:
      - "Signature validation passes"
      - "Request is processed"
      - "Response status is 202"
      - "Security check succeeds"

---
# WhatsApp数据处理测试组
TestFunction: whatsappDataProcessing
Cases:
  - CaseID: "I-WA-F01"
    Module: "whatsapp"
    Description: "Should extract message content correctly"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "data-processing"
      - "message-extraction"
    Precondition:
      - "Webhook payload contains message data"
      - "Message has text content"
    Steps:
      - "Send webhook with text message"
      - "Extract message content"
      - "Verify content extraction"
      - "Check data integrity"
    ExpectedResult:
      - "Message text is extracted correctly"
      - "Message metadata is preserved"
      - "Content is properly formatted"
      - "No data loss occurs"

  - CaseID: "I-WA-F02"
    Module: "whatsapp"
    Description: "Should extract dialogue information correctly"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "data-processing"
      - "dialogue-extraction"
    Precondition:
      - "Webhook payload contains dialogue data"
      - "Dialogue has recipient and agent info"
    Steps:
      - "Send webhook with dialogue information"
      - "Extract dialogue data"
      - "Verify recipient phone number"
      - "Check agent brand ID"
    ExpectedResult:
      - "Dialogue ID is extracted"
      - "Recipient phone is correct"
      - "Agent brand ID is preserved"
      - "Dialogue status is captured"

---
# WhatsApp集成服务测试组
TestFunction: whatsappIntegrationService
Cases:
  - CaseID: "I-WA-G01"
    Module: "whatsapp"
    Description: "Should integrate with session service"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "integration"
      - "session-service"
    Precondition:
      - "Session service is available"
      - "Redis is connected"
      - "User data exists"
    Steps:
      - "Process incoming message"
      - "Create or retrieve session"
      - "Update session data"
      - "Verify service integration"
    ExpectedResult:
      - "Session service is called correctly"
      - "Session data is managed properly"
      - "Integration works seamlessly"
      - "No service conflicts occur"

  - CaseID: "I-WA-G02"
    Module: "whatsapp"
    Description: "Should integrate with order FSM"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "integration"
      - "order-fsm"
    Precondition:
      - "Order FSM is available"
      - "Session exists"
      - "FSM is initialized"
    Steps:
      - "Send order-related message"
      - "Process through FSM"
      - "Verify FSM integration"
      - "Check state management"
    ExpectedResult:
      - "FSM processes message correctly"
      - "State transitions work properly"
      - "Order context is maintained"
      - "Integration is seamless"

---
# WhatsApp测试工具验证组
TestFunction: whatsappTestUtilities
Cases:
  - CaseID: "I-WA-H01"
    Module: "whatsapp"
    Description: "Should create mock webhook payload correctly"
    Importance: "Medium"
    Status: "Implemented"
    Tags:
      - "positive"
      - "test-utilities"
      - "mock-data"
    Precondition:
      - "WhatsApp helper is available"
      - "Mock data structure is defined"
    Steps:
      - "Create mock webhook payload"
      - "Verify payload structure"
      - "Check required fields"
      - "Validate data format"
    ExpectedResult:
      - "Mock payload is well-formed"
      - "All required fields are present"
      - "Data format matches specification"
      - "Payload can be used in tests"

  - CaseID: "I-WA-H02"
    Module: "whatsapp"
    Description: "Should generate valid phone numbers"
    Importance: "Low"
    Status: "Implemented"
    Tags:
      - "positive"
      - "test-utilities"
      - "data-generation"
    Precondition:
      - "WhatsApp helper is available"
      - "Faker library is loaded"
    Steps:
      - "Generate random phone number"
      - "Verify format"
      - "Check country code"
      - "Validate uniqueness"
    ExpectedResult:
      - "Phone number is generated"
      - "Format is correct"
      - "Country code is included"
      - "Number is valid for testing"
