#!/usr/bin/env node

/**
 * 日志压缩脚本
 * 将测试日志中的多行console.log输出压缩为单行格式
 */

const fs = require('fs');
const path = require('path');

class LogCompactor {
  constructor() {
    this.patterns = {
      // 匹配Act Log中的console.log多行输出
      actLogConsole: /(\[Act Log\] - \+\d+ms \[process\.(stdout|stderr)\]\s+)(console\.log[\s\S]*?)(?=\n\[|\n$)/g,
      // 匹配Jest的● Console块
      jestConsoleBlock: /(● Console[\s\S]*?)(?=\n\s*PASS|\n\s*FAIL|\n\s*●|\n$)/g
    };
  }

  /**
   * 压缩单个console.log块
   */
  compactConsoleBlock(match, prefix, streamType, content) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    if (lines.length <= 1) {
      return match; // 单行不需要压缩
    }

    // 查找console.log类型、内容和堆栈信息
    let consoleType = '';
    let logContent = '';
    let stackInfo = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.startsWith('console.')) {
        consoleType = line;
      } else if (line.startsWith('at ')) {
        stackInfo = line;
      } else if (line.length > 0 && !line.includes('PASS') && !line.includes('FAIL')) {
        logContent = logContent ? `${logContent} ${line}` : line;
      }
    }

    // 生成压缩格式
    if (consoleType) {
      const parts = [consoleType];
      if (logContent) parts.push(logContent);
      if (stackInfo) parts.push(stackInfo);
      return `${prefix}${parts.join('\t\t')}`;
    }

    return match; // 如果无法解析，保持原样
  }

  /**
   * 压缩Jest Console块
   */
  compactJestConsoleBlock(match) {
    const lines = match.split('\n');
    const compactedLines = [];
    
    let i = 0;
    while (i < lines.length) {
      const line = lines[i].trim();
      
      // 保留标题行
      if (line.includes('● Console') || line.includes('PASS') || line.includes('FAIL')) {
        compactedLines.push(lines[i]);
        i++;
        continue;
      }
      
      // 处理console.log块
      if (line.startsWith('console.')) {
        const consoleType = line;
        let content = '';
        let stackInfo = '';
        
        // 查找内容和堆栈信息
        i++;
        while (i < lines.length) {
          const currentLine = lines[i].trim();
          if (currentLine.startsWith('console.') || currentLine.includes('●')) {
            break;
          }
          if (currentLine.startsWith('at ')) {
            stackInfo = currentLine;
          } else if (currentLine.length > 0) {
            content = content ? `${content} ${currentLine}` : currentLine;
          }
          i++;
        }
        
        // 生成压缩格式
        const parts = [consoleType];
        if (content) parts.push(content);
        if (stackInfo) parts.push(stackInfo);
        compactedLines.push(`  ${parts.join('\t\t')}`);
        continue;
      }
      
      // 其他行保持原样
      if (line.length > 0) {
        compactedLines.push(lines[i]);
      }
      i++;
    }
    
    return compactedLines.join('\n');
  }

  /**
   * 处理单个日志文件
   */
  processLogFile(filePath) {
    try {
      console.log(`处理日志文件: ${filePath}`);
      
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;

      // 压缩Act Log中的console.log输出
      const originalContent = content;
      content = content.replace(this.patterns.actLogConsole, (match, prefix, streamType, consoleContent) => {
        const compacted = this.compactConsoleBlock(match, prefix, streamType, consoleContent);
        if (compacted !== match) {
          modified = true;
        }
        return compacted;
      });

      // 压缩Jest Console块
      content = content.replace(this.patterns.jestConsoleBlock, (match) => {
        const compacted = this.compactJestConsoleBlock(match);
        if (compacted !== match) {
          modified = true;
        }
        return compacted;
      });

      if (modified) {
        // 创建备份
        const backupPath = filePath + '.backup';
        fs.writeFileSync(backupPath, originalContent);
        
        // 写入压缩后的内容
        fs.writeFileSync(filePath, content);
        console.log(`✅ 已压缩: ${filePath} (备份: ${backupPath})`);
        
        return true;
      } else {
        console.log(`⏭️  无需压缩: ${filePath}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * 查找并处理所有日志文件
   */
  processAllLogFiles(reportsDir) {
    try {
      if (!fs.existsSync(reportsDir)) {
        console.log(`报告目录不存在: ${reportsDir}`);
        return;
      }

      const entries = fs.readdirSync(reportsDir);
      let processedCount = 0;
      let modifiedCount = 0;

      for (const entry of entries) {
        const entryPath = path.join(reportsDir, entry);
        const stat = fs.statSync(entryPath);

        if (stat.isDirectory() && entry.startsWith('test_')) {
          // 处理测试目录中的日志文件
          const logFiles = fs.readdirSync(entryPath).filter(file => 
            file.endsWith('.log') && !file.endsWith('.backup')
          );

          for (const logFile of logFiles) {
            const logPath = path.join(entryPath, logFile);
            processedCount++;
            if (this.processLogFile(logPath)) {
              modifiedCount++;
            }
          }
        }
      }

      console.log(`\n📊 处理完成:`);
      console.log(`   处理文件数: ${processedCount}`);
      console.log(`   修改文件数: ${modifiedCount}`);
      console.log(`   跳过文件数: ${processedCount - modifiedCount}`);

    } catch (error) {
      console.error('❌ 处理日志文件时出错:', error.message);
    }
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const reportsDir = args[0] || path.join(__dirname, '../reports');

  console.log('🚀 日志压缩脚本启动');
  console.log(`📁 报告目录: ${reportsDir}`);

  const compactor = new LogCompactor();
  compactor.processAllLogFiles(reportsDir);

  console.log('✅ 日志压缩完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = LogCompactor;
