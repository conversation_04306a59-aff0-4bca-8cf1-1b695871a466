const dotenv = require('dotenv');
const path = require('path');

/**
 * 根据环境变量加载对应的配置文件
 */
function loadEnvConfig() {
  const env = process.env.NODE_ENV;

  // 如果是在 Render 环境中运行，直接使用 Render 平台提供的环境变量
  if (env === 'render') {
    console.log('Running on Render.com, using environment variables from Render dashboard');
    return; // 跳过加载环境文件
  }

  let envFile;

  switch (env) {
    case 'local':
      envFile = '.env.local';
      break;
    case 'test':
      envFile = '.env.test';
      break;
    case 'production':
      envFile = '.env.prod';
      break;
    default:
      throw new Error(`Invalid NODE_ENV: ${env}`);
  }

  const result = dotenv.config({
    path: path.join(__dirname, envFile)
  });

  if (result.error) {
    throw new Error(`Error loading environment file ${envFile}: ${result.error.message}`);
  }

  console.log(`Loaded environment configuration from ${envFile}`);
}

/**
 * 验证必需的环境变量
 */
function validateEnvVariables(config) {
  // 检查是否在 Render 环境中运行
  const isRenderEnv = process.env.NODE_ENV === 'render';

  // 基本配置 - 这些变量在所有环境中都是必需的
  let coreRequiredVars = [
    'NODE_ENV',
    'LOG_LEVEL',
    'SERVER_URL',
    'PORT',
    'JWT_SECRET'
  ];

  // 在测试环境中，数据库连接由 testcontainers 管理，跳过验证
  if (process.env.NODE_ENV !== 'test') {
    coreRequiredVars = [
      ...coreRequiredVars,
      'CONNECTION_STRING',
      'REDIS_URL',
      'REDIS_ENABLE_TLS'
    ];
  }

  // Payemoji 认证配置 - 这些变量在所有环境中都是必需的
  const payemojiRequiredVars = [
    'PAYEMOJI_CLIENT_ID',
    'PAYEMOJI_CLIENT_SECRET',
    'PAYEMOJI_WEBHOOK_SECRET',
    'PAYEMOJI_AGENT_ID'
  ];

  // Stripe 相关配置
  const stripeRequiredVars = [
    'STRIPE_CHECKOUT_BASE_URL'
  ];

  // Dialog 会话配置
  const dialogRequiredVars = [
    'DIALOG_SESSION_TOKEN_BYTES',
    'DIALOG_SESSION_TTL_SECONDS'
  ];

  // Firebase 配置 - 在生产环境中必需
  const firebaseRequiredVars = [];
  if (process.env.NODE_ENV === 'render' || process.env.NODE_ENV === 'production') {
    firebaseRequiredVars.push('FIREBASE_SERVICE_ACCOUNT_KEY');
  }

  // WhatsApp 服务配置 - 这些变量在本地环境中是必需的，但在 Render 环境中可能使用不同的命名方式
  const whatsappRequiredVars = [
    // WhatsApp API 配置
    'WS_API_URL',
    'WS_AUTH_URL',
    'WS_BOUNDARY',

    // 认证相关配置
    'WS_GRANT_TYPE',
    'WS_SCOPE',
    'WS_B2C_POLICY',

    // 模板 ID 配置
    'WS_TEMPLATE_BASIC_TEXT',
    'WS_TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_HEADER_FOOTER',
    'WS_TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_IMAGE',
    'WS_TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_HEADER_FOOTER',
    'WS_TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_IMAGE',
    'WS_TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_HEADER_FOOTER',
    'WS_TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_IMAGE',
    'WS_TEMPLATE_LIST_PICKER_4',
    'WS_TEMPLATE_LIST_PICKER_5',
    'WS_TEMPLATE_LIST_PICKER_6',
    'WS_TEMPLATE_LIST_PICKER_7',
    'WS_TEMPLATE_LIST_PICKER_8',
    'WS_TEMPLATE_WHATSAPP_0_VARIABLES_NO_IMAGE',
    'WS_TEMPLATE_WHATSAPP_0_VARIABLES_WITH_IMAGE',
    'WS_TEMPLATE_WHATSAPP_0_VARIABLES_WITH_VIDEO',
    'WS_TEMPLATE_WHATSAPP_2_VARIABLES_NO_IMAGE',
    'WS_TEMPLATE_WHATSAPP_2_VARIABLES_WITH_IMAGE',
    'WS_TEMPLATE_WHATSAPP_3_VARIABLES_NO_IMAGE',
    'WS_TEMPLATE_WHATSAPP_3_VARIABLES_WITH_IMAGE',
    'WS_TEMPLATE_WHATSAPP_4_VARIABLES_NO_IMAGE',
    'WS_TEMPLATE_WHATSAPP_4_VARIABLES_WITH_IMAGE',
    'WS_TEMPLATE_WHATSAPP_FLOW_WITH_IMAGE',
    'WS_TEMPLATE_WHATSAPP_FLOW_FORM'
  ];

  // 如果在 Render 环境中运行，只检查核心变量、Payemoji 变量、Stripe 变量和 Dialog 变量
  // 因为 Render 环境中的 WhatsApp 配置可能使用不同的命名方式
  const requiredVars = isRenderEnv
    ? [...coreRequiredVars, ...payemojiRequiredVars, ...stripeRequiredVars, ...dialogRequiredVars, ...firebaseRequiredVars]
    : [...coreRequiredVars, ...payemojiRequiredVars, ...stripeRequiredVars, ...dialogRequiredVars, ...whatsappRequiredVars, ...firebaseRequiredVars];

  // 记录当前环境和要检查的变量
  console.log(`Validating environment variables in ${isRenderEnv ? 'Render' : 'local'} environment`);
  console.log(`Required variables: ${requiredVars.length}`);

  const missing = requiredVars.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

// 加载环境配置
loadEnvConfig();

// 创建配置对象
const config = {
  NODE_ENV: process.env.NODE_ENV,
  LOG_LEVEL: process.env.LOG_LEVEL,
  CONSOLE_LOG_LEVEL: process.env.CONSOLE_LOG_LEVEL || process.env.LOG_LEVEL,
  FILE_LOG_LEVEL: process.env.FILE_LOG_LEVEL || process.env.LOG_LEVEL,
  NODE_DEBUG: process.env.NODE_DEBUG,
  SERVER_URL: process.env.SERVER_URL,
  PORT: process.env.PORT,
  CONNECTION_STRING: process.env.NODE_ENV === 'test' ? global.__MONGO_URI__ : process.env.CONNECTION_STRING,
  RESET_PASSWORD_LINK: process.env.RESET_PASSWORD_LINK,

  // JWT 配置
  JWT_SECRET: process.env.JWT_SECRET,

  // Stripe 相关配置（独立出来）
  STRIPE: {
    SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    WEBHOOK_ENDPOINT_SECRET: process.env.STRIPE_WEBHOOK_ENDPOINT_SECRET,
    CHECKOUT_BASE_URL: process.env.STRIPE_CHECKOUT_BASE_URL
  },

  DASHBOARD_URL: process.env.DASHBOARD_URL,
  WEB_URL: process.env.WEB_URL,
  ORDER_DETAIL_WEB_URL: process.env.ORDER_DETAIL_WEB_URL,
  SENTRY_DSN: process.env.SENTRY_DSN,
  ZAPIER_WEBHOOK_URL: process.env.ZAPIER_WEBHOOK_URL,
  REDIS_URL: process.env.NODE_ENV === 'test' ? global.__REDIS_URI__ : process.env.REDIS_URL,
  REDIS_ENABLE_TLS: process.env.NODE_ENV === 'test' ? false : process.env.REDIS_ENABLE_TLS === 'true',
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,
  REDIS_HOST: process.env.REDIS_HOST,
  REDIS_PORT: process.env.REDIS_PORT,

  // CORS configuration
  CORS: {
    // Allowed domains, including imyth.org and all its subdomains
    ALLOWED_ORIGINS: process.env.CORS_ALLOWED_ORIGINS,
    // Whether to allow credentials (cookies, etc.)
    ALLOW_CREDENTIALS: process.env.CORS_ALLOW_CREDENTIALS === 'true',
    // Allowed HTTP methods
    ALLOWED_METHODS: process.env.CORS_ALLOWED_METHODS,
    // Allowed HTTP headers
    ALLOWED_HEADERS: process.env.CORS_ALLOWED_HEADERS
  },

  // Default currency configuration
  CURRENCY: {
    DEFAULT_CURRENCY: process.env.DEFAULT_CURRENCY,
    DEFAULT_CURRENCY_SYMBOL: process.env.DEFAULT_CURRENCY_SYMBOL
  },

  // WhatsApp 服务配置 - 扁平化结构
  WS: {
    // API 配置
    API_URL: process.env.WS_API_URL,
    AUTH_URL: process.env.WS_AUTH_URL,
    BOUNDARY: process.env.WS_BOUNDARY,

    // Payemoji 认证配置
    PAYEMOJI_CLIENT_ID: process.env.PAYEMOJI_CLIENT_ID,
    PAYEMOJI_CLIENT_SECRET: process.env.PAYEMOJI_CLIENT_SECRET,
    PAYEMOJI_WEBHOOK_SECRET: process.env.PAYEMOJI_WEBHOOK_SECRET,
    PAYEMOJI_AGENT_ID: process.env.PAYEMOJI_AGENT_ID,

    // 认证相关配置
    GRANT_TYPE: process.env.WS_GRANT_TYPE,
    SCOPE: process.env.WS_SCOPE,
    B2C_POLICY: process.env.WS_B2C_POLICY,

    // 服务 URL
    ORDER_MANAGEMENT_URL: process.env.ORDER_MANAGEMENT_URL,
    ADDRESS_MANAGEMENT_URL: process.env.ADDRESS_MANAGEMENT_URL,

    // 模板 ID
    TEMPLATE_BASIC_TEXT: process.env.WS_TEMPLATE_BASIC_TEXT,

    // Quick Reply 模板
    TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_HEADER_FOOTER: process.env.WS_TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_HEADER_FOOTER,
    TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_IMAGE: process.env.WS_TEMPLATE_QUICK_REPLY_1_BUTTON_WITH_IMAGE,
    TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_HEADER_FOOTER: process.env.WS_TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_HEADER_FOOTER,
    TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_IMAGE: process.env.WS_TEMPLATE_QUICK_REPLY_2_BUTTONS_WITH_IMAGE,
    TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_HEADER_FOOTER: process.env.WS_TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_HEADER_FOOTER,
    TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_IMAGE: process.env.WS_TEMPLATE_QUICK_REPLY_3_BUTTONS_WITH_IMAGE,

    // List Picker 模板
    TEMPLATE_LIST_PICKER_4: process.env.WS_TEMPLATE_LIST_PICKER_4,
    TEMPLATE_LIST_PICKER_5: process.env.WS_TEMPLATE_LIST_PICKER_5,
    TEMPLATE_LIST_PICKER_6: process.env.WS_TEMPLATE_LIST_PICKER_6,
    TEMPLATE_LIST_PICKER_7: process.env.WS_TEMPLATE_LIST_PICKER_7,
    TEMPLATE_LIST_PICKER_8: process.env.WS_TEMPLATE_LIST_PICKER_8,

    // WhatsApp 模板
    TEMPLATE_WHATSAPP_0_VARIABLES_NO_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_0_VARIABLES_NO_IMAGE,
    TEMPLATE_WHATSAPP_0_VARIABLES_WITH_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_0_VARIABLES_WITH_IMAGE,
    TEMPLATE_WHATSAPP_0_VARIABLES_WITH_VIDEO: process.env.WS_TEMPLATE_WHATSAPP_0_VARIABLES_WITH_VIDEO,
    TEMPLATE_WHATSAPP_2_VARIABLES_NO_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_2_VARIABLES_NO_IMAGE,
    TEMPLATE_WHATSAPP_2_VARIABLES_WITH_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_2_VARIABLES_WITH_IMAGE,
    TEMPLATE_WHATSAPP_3_VARIABLES_NO_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_3_VARIABLES_NO_IMAGE,
    TEMPLATE_WHATSAPP_3_VARIABLES_WITH_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_3_VARIABLES_WITH_IMAGE,
    TEMPLATE_WHATSAPP_4_VARIABLES_NO_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_4_VARIABLES_NO_IMAGE,
    TEMPLATE_WHATSAPP_4_VARIABLES_WITH_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_4_VARIABLES_WITH_IMAGE,

    // WhatsApp Flow 模板
    TEMPLATE_WHATSAPP_FLOW_WITH_IMAGE: process.env.WS_TEMPLATE_WHATSAPP_FLOW_WITH_IMAGE,
    TEMPLATE_WHATSAPP_FLOW_FORM: process.env.WS_TEMPLATE_WHATSAPP_FLOW_FORM,

    // 消息类型
    MESSAGE_TYPE_TEXT: 'text',
    MESSAGE_TYPE_INTERACTIVE: 'interactive',
    MESSAGE_TYPE_TEMPLATE: 'template',
    MESSAGE_TYPE_IMAGE: 'image',
    MESSAGE_TYPE_DOCUMENT: 'document',
    MESSAGE_TYPE_AUDIO: 'audio',
    MESSAGE_TYPE_VIDEO: 'video',
    MESSAGE_TYPE_STICKER: 'sticker',
    MESSAGE_TYPE_LOCATION: 'location',
    MESSAGE_TYPE_CONTACTS: 'contacts'
  },

  // Dialog 会话配置
  DIALOG: {
    SESSION_TOKEN_BYTES: parseInt(process.env.DIALOG_SESSION_TOKEN_BYTES, 10),
    SESSION_TTL_SECONDS: parseInt(process.env.DIALOG_SESSION_TTL_SECONDS, 10)
  },

  // Firebase 配置
  FIREBASE: {
    SERVICE_ACCOUNT_KEY: process.env.FIREBASE_SERVICE_ACCOUNT_KEY
  }
};

// 验证环境变量
validateEnvVariables(config);

module.exports = config;
