# Firespoon API Test Environment Configuration
# Copy this file to .env.test and fill in your values

# Node environment
NODE_ENV=test

# Database Configuration
# MongoDB 7.0 和 Redis 7.2.4 通过 testcontainers 自动管理
# 无需手动配置数据库连接字符串

# JWT Secret for authentication (REQUIRED)
# Generate a strong secret key for JWT token signing
# Example: openssl rand -base64 32
JWT_SECRET=test-jwt-secret-change-in-production

# WhatsApp API Configuration
WS_API_URL=https://test-api.example.com
WS_AUTH_URL=https://test-auth.example.com
WS_BOUNDARY=test-boundary

# Payemoji credentials
PAYEMOJI_CLIENT_ID=test-client-id
PAYEMOJI_CLIENT_SECRET=test-client-secret
PAYEMOJI_WEBHOOK_SECRET=test-webhook-secret
PAYEMOJI_AGENT_ID=test-agent-id

# Stripe API keys
STRIPE_SECRET_KEY=sk_test_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret

# Firebase Configuration (REQUIRED for production)
# For production/render environment, set this to the entire JSON content of your service account key
# For development, you can either use this environment variable or place serviceAccountKey.json in project root
# FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-project",...}'

# Logging
LOG_LEVEL=debug
