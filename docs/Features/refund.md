# 退款系统功能文档

## 概述

Firespoon API 的退款系统提供完整的订单退款处理功能，支持全额退款和部分退款，集成 Stripe 支付处理，并提供完整的退款状态跟踪。

## 核心功能

### 1. 退款类型
- **全额退款**: 退还订单的全部金额
- **部分退款**: 退还订单的部分金额

### 2. 退款状态跟踪

#### Order Model 中的 refundStatus 字段
- **类型**: String
- **枚举值**: ['NONE', 'PARTIAL', 'FULL']
- **默认值**: 'NONE'
- **说明**:
  - `NONE`: 订单未进行任何退款（默认状态）
  - `PARTIAL`: 订单已进行部分退款
  - `FULL`: 订单已进行全额退款

#### 相关字段
- `totalRefunded`: Number (默认: 0) - 已退款总金额
- `refunds`: Array of ObjectId - 退款记录引用数组

### 3. 退款原因分类
- **商家原因**: 商品缺货、餐厅关闭等
- **客户原因**: 客户取消、地址错误等
- **系统原因**: 技术故障、配送问题等

### 4. 费用承担规则
- **商家原因**: 商家承担手续费
- **客户原因**: 客户承担手续费
- **系统原因**: 平台承担手续费

## GraphQL API

### 类型定义

#### OrderRefundStatus 枚举
```graphql
enum OrderRefundStatus {
  NONE
  PARTIAL
  FULL
}
```

#### Order 类型中的退款字段
```graphql
type Order {
  # ... 其他字段
  refundStatus: OrderRefundStatus!
  totalRefunded: Float!
  refunds: [Refund!]!
}
```

### 查询示例

#### 查询订单退款状态
```graphql
query GetOrderRefundStatus($orderId: ID!) {
  order(id: $orderId) {
    orderId
    orderAmount
    refundStatus
    totalRefunded
    refunds {
      id
      refundAmount
      reason
      status
    }
  }
}
```

#### 查询需要退款的订单
```graphql
query GetRefundableOrders {
  orders(filter: { refundStatus: NONE, orderStatus: PAID }) {
    orderId
    orderAmount
    refundStatus
    customerPhone
  }
}
```

## 使用示例

### 检查订单退款状态
```javascript
// 检查订单是否有退款
if (order.refundStatus === 'NONE') {
  console.log('订单未进行退款');
} else if (order.refundStatus === 'PARTIAL') {
  console.log(`订单已部分退款: $${order.totalRefunded}`);
} else if (order.refundStatus === 'FULL') {
  console.log('订单已全额退款');
}
```

### 更新退款状态
```javascript
// 处理退款后更新订单状态
order.refunds.push(refundId);
order.totalRefunded += refundAmount;

// 根据退款金额确定状态
if (order.totalRefunded >= order.orderAmount) {
  order.refundStatus = 'FULL';
} else if (order.totalRefunded > 0) {
  order.refundStatus = 'PARTIAL';
}

await order.save();
```

## 业务规则

### 1. 退款状态自动更新
- 当 `totalRefunded = 0` 时，`refundStatus = 'NONE'`
- 当 `0 < totalRefunded < orderAmount` 时，`refundStatus = 'PARTIAL'`
- 当 `totalRefunded >= orderAmount` 时，`refundStatus = 'FULL'`

### 2. 数据一致性
- `totalRefunded` 字段必须等于所有关联退款记录的金额总和
- `refunds` 数组必须包含所有相关的退款记录引用
- `refundStatus` 必须反映当前的实际退款状态

### 3. 验证规则
- `totalRefunded` 不能为负数
- `totalRefunded` 不能超过 `orderAmount`
- `refundStatus` 必须是有效的枚举值

## 错误处理

### 常见错误场景
1. **退款金额超过订单金额**
   - 错误码: REFUND_AMOUNT_EXCEEDS_ORDER
   - 处理: 拒绝退款请求

2. **重复退款**
   - 错误码: DUPLICATE_REFUND
   - 处理: 检查现有退款记录

3. **无效的退款状态**
   - 错误码: INVALID_REFUND_STATUS
   - 处理: 验证枚举值

## 监控和报告

### 关键指标
- 退款率 (按状态分类)
- 平均退款金额
- 退款处理时间
- 退款原因分布

### 查询统计
```javascript
// 统计各种退款状态的订单数量
const refundStats = await Order.aggregate([
  {
    $group: {
      _id: '$refundStatus',
      count: { $sum: 1 },
      totalAmount: { $sum: '$totalRefunded' }
    }
  }
]);
```

## 最佳实践

1. **状态一致性**: 始终确保 `refundStatus` 与 `totalRefunded` 保持一致
2. **原子操作**: 使用事务确保退款相关字段的原子更新
3. **审计跟踪**: 记录所有退款状态变更的历史
4. **性能优化**: 为 `refundStatus` 字段创建索引以提高查询性能
5. **数据验证**: 在应用层和数据库层都进行退款数据验证