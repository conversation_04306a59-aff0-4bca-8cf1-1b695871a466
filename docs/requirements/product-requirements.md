# Firespoon API - Product Requirements Document

## Project Background

Firespoon API is a comprehensive Node.js-based backend system for a food delivery platform. The system provides complete restaurant management, order processing, payment integration, and WhatsApp messaging services. The platform adopts a GraphQL API architecture to support multiple client applications and third-party service integrations.

### Business Context
The food delivery market requires a flexible, scalable platform that can serve multiple stakeholders:
- **Customers**: Need convenient ordering through mobile apps and WhatsApp
- **Restaurants**: Require efficient order management and menu control
- **Delivery Riders**: Need real-time order tracking and route optimization
- **Platform Operators**: Require comprehensive analytics and system management

### Project Goals
1. Provide a unified API platform for food delivery operations
2. Enable conversational ordering through WhatsApp integration
3. Support multiple payment gateways for flexible payment options
4. Ensure scalable architecture for business growth
5. Maintain high availability and performance standards

## User Stories

### Customer User Stories
**As a customer, I want to:**
- Browse restaurants and menus through mobile app or WhatsApp
- Place orders with customizable food items and addons
- Choose from multiple payment methods (Stripe, PayPal, Cash on Delivery)
- Track my order status in real-time
- Manage my delivery addresses and order history
- Receive notifications about order updates

**Priority**: Critical

### Restaurant Owner User Stories
**As a restaurant owner, I want to:**
- Manage my restaurant profile and menu items
- Receive and process incoming orders
- Update order status and delivery information
- View sales analytics and earnings reports
- Configure delivery zones and operating hours
- Manage food categories, options, and addons

**Priority**: Critical

### Delivery Rider User Stories
**As a delivery rider, I want to:**
- Receive order assignments based on my location
- Access customer and restaurant contact information
- Update delivery status and completion
- Track my earnings and delivery history
- Manage my availability status

**Priority**: High

### Platform Administrator User Stories
**As a platform administrator, I want to:**
- Monitor system performance and health
- Manage user accounts and permissions
- Configure system settings and payment gateways
- Access comprehensive analytics and reports
- Handle customer support and dispute resolution

**Priority**: High

## Functional Requirements

### FR-001: Restaurant Management
**Description**: System shall provide comprehensive restaurant management capabilities
**Priority**: Critical
**Requirements**:
- Restaurant owners can create and update restaurant profiles
- Support for menu management with categories, food items, and pricing
- Configuration of delivery zones and operating hours
- Management of food options, addons, and variations
- Real-time availability status updates

### FR-002: Order Processing
**Description**: System shall handle complete order lifecycle management
**Priority**: Critical
**Requirements**:
- Customers can place orders through multiple channels (mobile app, WhatsApp)
- Real-time order status tracking and updates
- Order validation including availability and delivery zone checks
- Support for order modifications and cancellations
- Integration with payment processing systems

### FR-003: Customer Management
**Description**: System shall provide customer account and profile management
**Priority**: Critical
**Requirements**:
- Customer registration and authentication
- Profile management with contact information
- Multiple delivery address management
- Order history and favorites tracking
- Notification preferences configuration

### FR-004: Payment Integration
**Description**: System shall support multiple payment methods and processing
**Priority**: Critical
**Requirements**:
- Integration with Stripe for credit/debit card payments
- PayPal payment processing support
- Cash on Delivery (COD) option
- Secure payment data handling and PCI compliance
- Refund and dispute management capabilities

### FR-005: WhatsApp Integration
**Description**: System shall enable conversational ordering through WhatsApp
**Priority**: High
**Requirements**:
- Webhook-based message processing
- State machine-driven conversation flow
- Menu browsing and item selection via chat
- Order placement and payment through WhatsApp
- Real-time order status notifications

### FR-006: Delivery Management
**Description**: System shall provide delivery tracking and rider management
**Priority**: High
**Requirements**:
- Rider registration and profile management
- Order assignment based on location and availability
- Real-time delivery tracking and status updates
- Delivery fee calculation based on distance and zones
- Rider earnings and performance tracking

### FR-007: Analytics and Reporting
**Description**: System shall provide comprehensive analytics and reporting
**Priority**: Medium
**Requirements**:
- Restaurant sales and performance analytics
- Customer behavior and ordering patterns
- Delivery performance metrics
- Financial reporting and reconciliation
- System health and performance monitoring

## Business Rules

### BR-001: Order Validation Rules
- Orders can only be placed during restaurant operating hours
- Delivery address must be within restaurant's delivery zone
- Minimum order amount may apply based on restaurant configuration
- Food items must be available and in stock
- Payment must be confirmed before order processing begins

### BR-002: Payment Processing Rules
- All payments must be processed through secure, PCI-compliant gateways
- Refunds can only be initiated by authorized personnel
- Payment failures must trigger automatic order cancellation
- Transaction records must be maintained for audit purposes

### BR-003: WhatsApp Integration Rules
- Session timeout after 30 minutes of inactivity
- Maximum of 5 concurrent orders per customer
- Message rate limiting to prevent spam
- Conversation state must be preserved across message exchanges

### BR-004: Data Privacy Rules
- Customer personal information must be encrypted at rest
- Payment data must not be stored locally
- User consent required for marketing communications
- Data retention policies must be enforced

## User Experience

### Mobile Application Experience
**Target Users**: Customers, Restaurant Owners, Delivery Riders
**Key Features**:
- Intuitive restaurant and menu browsing with search and filters
- Streamlined order placement with real-time price calculation
- Multiple payment options with secure checkout process
- Real-time order tracking with push notifications
- User-friendly profile and address management

**Performance Requirements**:
- App launch time < 3 seconds
- Search results displayed within 2 seconds
- Order placement completion < 30 seconds
- Real-time updates with < 5 second latency

### WhatsApp Conversational Experience
**Target Users**: Customers preferring messaging interface
**Key Features**:
- Natural language interaction for order placement
- Interactive buttons and lists for menu navigation
- Automated order confirmation and payment links
- Real-time status updates via WhatsApp messages
- Fallback to human support when needed

**Conversation Flow**:
1. Welcome message with restaurant selection
2. Menu browsing with category navigation
3. Item selection with customization options
4. Cart review and address confirmation
5. Payment processing and order confirmation
6. Real-time status updates until delivery

### Web Dashboard Experience
**Target Users**: Restaurant Owners, Platform Administrators
**Key Features**:
- Comprehensive order management dashboard
- Real-time analytics and reporting
- Menu and restaurant profile management
- Customer support and communication tools
- System configuration and settings

**Responsive Design**:
- Optimized for desktop and tablet devices
- Mobile-friendly responsive layout
- Accessibility compliance (WCAG 2.1)
- Cross-browser compatibility

## 部署架构

### 生产环境
- **应用服务器**: Node.js 集群
- **数据库**: MongoDB Atlas
- **缓存**: Redis Cloud
- **CDN**: 静态资源分发
- **监控**: Sentry + 自定义日志

### 开发环境
- **本地开发**: Docker Compose
- **测试环境**: Jest + MongoDB Memory Server
- **CI/CD**: GitHub Actions (推荐)

## 安全特性

### 数据安全
- 密码加密存储
- JWT token 管理
- API 请求验证
- 输入数据验证

### 网络安全
- CORS 配置
- Rate Limiting
- HTTPS 强制
- Webhook 签名验证

### 业务安全
- 订单状态验证
- 支付金额验证
- 地址权限检查
- 会话超时管理

## 性能优化

### 数据库优化
- MongoDB 索引优化
- 查询性能监控
- 连接池管理
- 数据分页

### 缓存策略
- Redis 会话缓存
- 餐厅数据缓存
- 查询结果缓存
- 静态资源缓存

### API 优化
- GraphQL 查询优化
- 批量数据加载
- 响应压缩
- 请求去重

## 监控和日志

### 应用监控
- Sentry 错误跟踪
- 性能指标监控
- 实时告警
- 用户行为分析

### 日志管理
- Winston 结构化日志
- 日志轮转
- 错误级别分类
- 调试信息记录

## 开发指南

### 环境设置
1. 安装 Node.js 18+
2. 安装 MongoDB 和 Redis
3. 配置环境变量
4. 运行 `npm install`
5. 启动开发服务器

### 代码规范
- ESLint 代码检查
- Prettier 代码格式化
- Git commit 规范
- 单元测试覆盖

### API 开发
- GraphQL Schema First
- Resolver 模式
- 错误处理标准
- 文档自动生成

## 扩展性设计

### 水平扩展
- 无状态应用设计
- 负载均衡支持
- 数据库分片准备
- 微服务架构兼容

### 功能扩展
- 插件化架构
- 模块化设计
- 配置驱动
- API 版本管理

## 项目结构概览

```
firespoon-api/
├── app.js                 # 应用入口
├── config.js              # 配置管理
├── graphql/               # GraphQL 相关
│   ├── schema/           # Schema 定义
│   └── resolvers/        # Resolver 实现
├── models/               # 数据模型
├── middleware/           # 中间件
├── routes/               # REST 路由
├── whatsapp/             # WhatsApp 集成
├── helpers/              # 工具函数
├── test/                 # 测试文件
└── Doc/                  # 项目文档
```

---

# 详细模块文档

## GraphQL API 模块

### 核心组件

#### Schema 定义 (`graphql/schema/`)
- **base.graphql**: 基础类型和标量定义
- **extra.graphql**: 扩展查询和变更定义
- **各实体类型**: Restaurant, Order, Customer, Food 等

#### Resolvers (`graphql/resolvers/`)

##### 主要 Resolver 模块
- **authResolver**: 用户认证和授权
- **orderResolver**: 订单管理和处理
- **restaurantResolver**: 餐厅信息管理
- **foodResolver**: 菜品管理
- **customerResolvers**: 客户管理
- **addressResolver**: 地址管理
- **brandResolver**: 品牌管理

##### 核心函数调用关系

**订单处理流程**:
```
orderResolver.placeOrder()
├── validateOrderData() - 验证订单数据
├── calculateOrderTotal() - 计算订单总价
├── checkRestaurantAvailability() - 检查餐厅可用性
├── processPayment() - 处理支付
└── createOrderRecord() - 创建订单记录
```

**餐厅查询流程**:
```
restaurantResolver.restaurants()
├── applyLocationFilter() - 应用位置过滤
├── applyAvailabilityFilter() - 应用可用性过滤
├── loadRestaurantData() - 加载餐厅数据
└── transformRestaurantData() - 转换数据格式
```

### 认证和授权

#### JWT 认证流程
```
1. 客户端请求 → isAuthenticated() 中间件
2. 提取 Authorization header
3. 验证 JWT token
4. 解析用户信息 (userId, userType, restaurantId)
5. 设置请求上下文
```

#### WhatsApp 认证流程
```
1. 检查 X-WhatsAppW-Token header
2. processWhatsAppAuth() 处理
3. sessionService.getSessionByToken() 验证
4. 设置 WhatsApp 上下文 (brandref, customerPhone)
```

## 数据模型模块 (`models/`)

### 核心实体模型

#### Restaurant 模型
- **字段**: name, address, categories, options, addons, deliveryBounds
- **索引**: deliveryBounds (2dsphere), keywords
- **关系**: categories[], options[], addons[]

#### Order 模型
- **字段**: customer, restaurant, items, deliveryAddress, paymentStatus
- **状态**: PENDING, CONFIRMED, PREPARING, DISPATCHED, DELIVERED, CANCELLED
- **关系**: customer (ref), restaurant (ref), items[]

#### Customer 模型
- **字段**: phone, name, email, addresses, orderHistory
- **认证**: phone 作为主要标识符
- **关系**: addresses[], orders[]

#### Food 模型
- **字段**: title, description, price, category, restaurant, variations
- **状态**: isActive, inStock
- **关系**: category (ref), restaurant (ref), variations[]

### 数据关系图
```
Brand 1:N Restaurant 1:N Category 1:N Food
  │                     │
  │                     └── 1:N Option
  │                     └── 1:N Addon
  │
  └── 1:N Customer 1:N Order N:1 Restaurant
              │         │
              └── 1:N Address
                        └── N:1 Order (deliveryAddress)
```

## 支付处理模块

### Stripe 集成 (`routes/stripe.js`)

#### 核心函数
- **createPaymentIntent()**: 创建支付意图
- **handleWebhook()**: 处理 Stripe webhook
- **processRefund()**: 处理退款

#### 支付流程
```
1. 客户端请求支付 → createPaymentIntent()
2. 返回 client_secret
3. 客户端确认支付
4. Stripe webhook → handleWebhook()
5. 更新订单状态
```

### PayPal 集成 (`routes/paypal.js`)

#### 核心函数
- **createPayment()**: 创建 PayPal 支付
- **executePayment()**: 执行支付
- **handleIPN()**: 处理即时支付通知

## 工具和辅助模块

### 日志系统 (`helpers/logger.js`)
- **Winston**: 结构化日志记录
- **日志级别**: error, warn, info, debug
- **日志轮转**: 按日期轮转日志文件
- **格式化**: JSON 格式便于分析

### 通知系统 (`helpers/notifications.js`)
- **Firebase**: 推送通知
- **Email**: 邮件通知
- **SMS**: 短信通知 (Twilio)

### 地理位置服务 (`helpers/location.js`)
- **距离计算**: 计算配送距离
- **区域检查**: 检查配送范围
- **地址验证**: 验证地址格式

## 中间件模块 (`middleware/`)

### 认证中间件 (`is-auth.js`)
```javascript
function isAuthenticated(req) {
  // 1. 提取 JWT token
  // 2. 验证 token 有效性
  // 3. 解析用户信息
  // 4. 返回认证状态
}
```

### WhatsApp 认证中间件 (`whatsapp-graphql-auth.js`)
```javascript
async function processWhatsAppAuth(context) {
  // 1. 检查 WhatsApp token
  // 2. 验证会话有效性
  // 3. 设置 WhatsApp 上下文
  // 4. 返回增强的上下文
}
```

## 配置管理

### 环境配置 (`config.js`)
- **数据库连接**: MongoDB 连接字符串
- **Redis 配置**: 缓存和会话存储
- **API 密钥**: 第三方服务密钥
- **CORS 设置**: 跨域请求配置

### 配置分类
```javascript
module.exports = {
  // 数据库配置
  CONNECTION_STRING: process.env.CONNECTION_STRING,
  DB_NAME: process.env.DB_NAME,

  // Redis 配置
  REDIS_URL: process.env.REDIS_URL,

  // 支付配置
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
  PAYPAL_CLIENT_ID: process.env.PAYPAL_CLIENT_ID,

  // WhatsApp 配置
  WS_API_URL: process.env.WS_API_URL,
  WS_CLIENT_ID: process.env.WS_CLIENT_ID,

  // 应用配置
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development'
};
```
