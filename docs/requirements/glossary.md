# Firespoon API - 项目词汇表

## 业务术语

### 核心实体

**Restaurant (餐厅)**
- 在平台上注册的餐饮商户
- 拥有菜单、食品项目和配送区域

**Customer (客户)**
- 使用平台订餐的终端用户
- 可通过移动应用或WhatsApp下单

**Rider (骑手)**
- 负责配送订单的配送员
- 具有配送区域和可用状态

**Order (订单)**
- 客户的购买请求
- 包含食品项目、配送地址和支付信息

**Food (食品)**
- 餐厅提供的菜品项目
- 包含价格、描述和可选配置

### 订单状态

**PENDING** - 待处理
- 订单已创建，等待餐厅确认

**ACCEPTED** - 已接受
- 餐厅已确认订单，开始准备

**PREPARING** - 准备中
- 餐厅正在制作食品

**READY** - 准备完成
- 食品制作完成，等待配送

**PICKED** - 已取餐
- 骑手已从餐厅取餐

**DELIVERED** - 已配送
- 订单已成功配送给客户

**CANCELLED** - 已取消
- 订单被取消（客户或餐厅）

### 支付方式

**COD (Cash on Delivery)** - 货到付款
- 客户在收货时现金支付

**STRIPE** - Stripe支付
- 在线信用卡/借记卡支付

**PAYPAL** - PayPal支付
- 通过PayPal账户支付

## 技术术语

### 架构组件

**GraphQL**
- API查询语言和运行时
- 提供类型安全的API接口

**Apollo Server**
- GraphQL服务器实现
- 处理GraphQL查询和变更

**Mongoose**
- MongoDB对象文档映射器
- 提供数据模型和验证

**Redis**
- 内存数据存储
- 用于缓存和会话管理

### WhatsApp集成

**Payemoji**
- 第三方WhatsApp消息服务提供商
- 支持对话式订餐

**Session (会话)**
- WhatsApp用户的对话状态
- 存储订单进度和用户选择

**Template Message**
- 预定义的WhatsApp消息模板
- 用于结构化信息传递

### 数据模型

**Schema (模式)**
- GraphQL类型定义
- 定义API的数据结构

**Resolver (解析器)**
- GraphQL字段的实现函数
- 处理数据获取和业务逻辑

**Model (模型)**
- Mongoose数据模型
- 定义MongoDB文档结构

**Middleware (中间件)**
- 请求处理管道组件
- 处理认证、日志等横切关注点

### 配送管理

**Zone (配送区域)**
- 地理配送范围
- 决定餐厅和骑手的服务区域

**Point (地理坐标)**
- 经纬度坐标
- 用于位置计算和路径规划

**Delivery Charges (配送费)**
- 基于距离和区域的配送费用
- 可配置的费率计算

### 系统配置

**Configuration (系统配置)**
- 全局系统设置
- 包含支付、通知、API密钥等配置

**Environment Variables (环境变量)**
- 部署时的配置参数
- 区分开发、测试、生产环境

**JWT (JSON Web Token)**
- 用户认证令牌
- 无状态的身份验证机制

## 缩写词汇

**API** - Application Programming Interface (应用程序编程接口)
**CRUD** - Create, Read, Update, Delete (创建、读取、更新、删除)
**ODM** - Object Document Mapper (对象文档映射器)
**ORM** - Object Relational Mapping (对象关系映射)
**REST** - Representational State Transfer (表述性状态转移)
**SOP** - Standard Operating Procedure (标准操作程序)
**TDD** - Test Driven Development (测试驱动开发)
**CI/CD** - Continuous Integration/Continuous Deployment (持续集成/持续部署)
