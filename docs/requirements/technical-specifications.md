
# Firespoon API - 技术规格文档

## 1. 文档结构大纲

### 1.1 简介
- **项目概述**: Firespoon API 是一个为食品配送平台提供后端服务的项目。
- **目的与范围**: 提供用于食品订购、餐厅管理和 WhatsApp 集成的 GraphQL API。
- **目标受众**: 开发者、系统管理员和业务相关人员。

### 1.2 入门指南
- **先决条件**: Node.js、MongoDB、Redis。
- **安装**: 安装说明和环境配置。
- **配置**: 环境变量和配置文件。
- **运行应用程序**: 开发、测试和生产模式。

### 1.3 核心组件
- **GraphQL API**: Schema、解析器和数据模型。
- **认证系统**: 基于 JWT 的认证和授权。
- **WhatsApp 集成**: Webhook 处理、对话管理和订单处理。
- **支付处理**: Stripe 和 PayPal 集成。
- **通知系统**: 电子邮件、短信和推送通知。

### 1.4 数据模型
- **用户管理**: 顾客、餐厅所有者、骑手和管理员。
- **餐厅管理**: 餐厅、菜单、类别和食品项。
- **订单处理**: 订单、订单项、附加项和规格。
- **地址和位置**: 顾客地址、餐厅位置和配送区域。
- **支付和交易**: 支付方式、交易记录和收入。

### 1.5 API 参考
- **GraphQL Schema**: 类型、查询、变更和订阅。
- **REST 端点**: 支付 Webhook 和 WhatsApp 集成。
- **认证**: 令牌生成和验证。
- **错误处理**: 错误代码和响应格式。

### 1.6 WhatsApp 集成
- **Webhook 设置**: 配置和认证。
- **对话流程**: 对话状态和转换。
- **订单处理**: 购物车管理和下单。
- **支付处理**: 支付确认和状态更新。
- **会话管理**: 顾客会话跟踪和状态持久化。

### 1.7 部署
- **环境设置**: 开发、预发布和生产环境。
- **数据库配置**: MongoDB 设置和索引。
- **缓存策略**: Redis 实现。
- **监控与日志**: Sentry 集成和 Winston 日志。
- **安全注意事项**: 认证、数据保护和 CORS。

### 1.8 测试
- **单元测试**: Jest 配置和测试覆盖率。
- **集成测试**: API 测试和 Webhook 模拟。
- **性能测试**: 负载测试和基准测试。

### 1.9 故障排除
- **常见问题**: 已知问题和解决方案。
- **日志与调试**: 访问和解读日志。
- **支持资源**: 联系信息和社区资源。

## 2. 系统架构文档

### 2.1 系统概述

Firespoon API 是一个基于 Node.js 的后端服务，为食品配送平台提供 GraphQL API。该系统集成了 WhatsApp 以支持会话式订购，支持多种支付网关，并提供实时订单跟踪。架构遵循模块化设计，关注点分离清晰。

### 2.2 技术栈

- **运行时环境**: Node.js
- **API 框架**: Express.js 与 Apollo Server (GraphQL)
- **数据库**: MongoDB 与 Mongoose ODM
- **缓存**: Redis 用于会话管理和数据缓存
- **消息队列**: Bull 用于后台作业处理
- **认证**: JWT (JSON Web Tokens)
- **支付处理**: Stripe 和 PayPal
- **消息传递**: WhatsApp Business API (WABA)
  - 使用 REST API 发送和接收消息
  - 需要通过 OAuth 2.0 令牌进行认证
  - 支持文本、交互式消息和媒体内容
  - 基于 Webhook 的事件通知系统
  - 用于结构化通信的消息模板
- **通知**: Firebase Cloud Messaging、SendGrid (电子邮件)、Twilio (短信)
- **监控**: Sentry 用于错误跟踪和性能监控
- **日志**: Winston，带每日轮换功能
- **测试**: Jest 用于单元和集成测试

### 2.3 系统组件

#### 2.3.1 核心 API 层

核心 API 层构建于 Express.js 和 Apollo Server 之上，为客户端应用程序提供 GraphQL 接口。关键组件包括：

- **GraphQL Schema**: 定义在 `graphql/schema/types/` 下的 `.graphql` 文件中。
- **解析器 (Resolvers)**: 在 `graphql/resolvers/` 下的 JavaScript 文件中实现。
- **中间件 (Middleware)**: 在 `middleware/` 中进行认证和请求处理。
- **数据模型 (Data Models)**: 在 `models/` 中的 Mongoose schema。

#### 2.3.2 WhatsApp 集成

WhatsApp 集成通过 WhatsApp 实现会话式订购。关键组件包括：

##### ******* Webhook API 规范

- **Webhook 端点**: `/whatsapp/webhook` (POST)
  - **认证**: 通过 `whatsappAuth` 中间件进行 HMAC 签名验证。
  - **请求头 (Headers)**:
    - `X-WABA-Signature`: 请求体的 HMAC 签名。
    - `Content-Type`: `application/vnd.api+json`

- **Webhook 请求负载结构**:
  ```json
  {
    "data": {
      "type": "webhook_event",
      "id": "event-uuid",
      "attributes": {
        "type": "messages.received" | "messages.status_update" | "dialogues.status_update"
      }
    },
    "included": [
      {
        "type": "messages",
        "id": "message-id",
        "attributes": {
          "content": [
            {
              "displayType": "text",
              "attrs": {
                "text": "消息内容"
              },
              "payload": {
                "externalID": "可选的外部ID"
              }
            }
          ]
        }
      },
      {
        "type": "dialogues",
        "id": "dialogue-id",
        "attributes": {
          "status": "ACTIVE" | "CLOSED"
        },
        "relationships": {
          "recipient": {
            "data": {
              "id": "recipient-id",
              "type": "customers"
            }
          },
          "agent": {
            "data": {
              "id": "brand-whatsapp-id",
              "type": "agents"
            }
          }
        }
      }
    ]
  }
  ```

- **Webhook 响应**:
  - 状态码: `202 Accepted` (异步处理)
  - 空响应体

##### ******* WhatsApp API 出站请求

- **基础 URL**: 在环境变量中配置。
- **认证**: OAuth 2.0 不记名令牌 (Bearer token)。
- **发送消息端点**: `POST /messages`
  - **请求头 (Headers)**:
    - `Authorization`: `Bearer {token}`
    - `Content-Type`: `application/vnd.api+json`
    - `Accept`: `application/vnd.api+json`

  - **发送文本消息的请求体**:
    ```json
    {
      "data": {
        "type": "messages",
        "attributes": {
          "dialogueId": "dialogue-id",
          "content": [
            {
              "displayType": "text",
              "attrs": {
                "text": "消息文本"
              }
            }
          ]
        }
      }
    }
    ```

  - **发送交互式消息的请求体**:
    ```json
    {
      "data": {
        "type": "messages",
        "attributes": {
          "dialogueId": "dialogue-id",
          "content": [
            {
              "displayType": "interactive",
              "attrs": {
                "type": "button",
                "header": "头部文本",
                "body": "消息正文",
                "footer": "尾部文本",
                "buttons": [
                  {
                    "type": "reply",
                    "title": "按钮文本",
                    "payload": "button_payload"
                  }
                ]
              }
            }
          ]
        }
      }
    }
    ```

##### 2.3.2.3 购物车提交 API

- **端点**: `/whatsapp/submit-cart` (POST)
  - **认证**: 通过 `sessionValidator` 中间件进行会话令牌验证。
  - **请求头 (Headers)**:
    - `X-WhatsApp-Token`: 会话令牌。
    - `Content-Type`: `application/json`

  - **请求体**:
    ```json
    {
      "restaurantId": "restaurant-id",
      "orderInput": [
        {
          "food": "food-id",
          "variation": "variation-id",
          "quantity": 1,
          "addons": [
            {
              "_id": "addon-id",
              "options": ["option-id-1", "option-id-2"]
            }
          ],
          "specialInstructions": "可选的特殊说明"
        }
      ],
      "isPickedUp": true|false,
      "deliveryAddressId": "address-id",
      "tipping": 0.00,
      "taxationAmount": 0.00,
      "instructions": "订单说明"
    }
    ```

  - **响应**:
    - 成功: `200 OK`，空响应体。
    - 错误: `400 Bad Request`，包含错误详情。

##### ******* Webhook 控制器

- **Webhook 控制器** (`whatsapp/controllers/webhookController.js`):
  - 处理来自 WhatsApp Business API 的传入消息和事件。
  - 处理三种主要事件类型：`messages.received`、`messages.status_update` 和 `dialogues.status_update`。
  - 提取消息内容、对话 ID 和收件人信息。
  - 为每个对话创建或检索会话数据。
  - 将消息路由到对话管理器进行处理。

- **对话管理器** (`whatsapp/machines/dialog.js`):
  - 采用状态机方法实现对话流程。
  - 管理不同的对话状态（例如，餐厅选择、地址选择、订单确认）。
  - 使用 `messageToEventMap` 将传入消息映射到特定事件类型。
  - 通过专用处理程序（例如 `handleCartUpdate`、`handleOrderPlaced`）处理事件。
  - 在整个对话生命周期中维护上下文。

- **会话服务** (`whatsapp/services/sessionService.js`):
  - 在 Redis 中维护用户会话状态，具有可配置的 TTL（生存时间）。
  - 提供创建、检索、更新和删除会话的方法。
  - 实现会话事件队列以进行异步处理。
  - 处理会话持久化和恢复。
  - 存储对话上下文、顾客信息和订单详情。

- **餐厅存储** (`whatsapp/services/restaurantStore.js`):
  - 缓存餐厅和菜单数据以便快速访问。
  - 提供按 ID 检索餐厅和品牌信息的方法。
  - 维护内存缓存，并定期从数据库刷新。
  - 为缓存未命中实现回退机制。
  - 优化 WhatsApp 订购流程的菜单数据检索。

- **消息构建器** (`whatsapp/services/messageBuilders.js`):
  - 为 WhatsApp 构建格式化消息。
  - 支持多种消息类型：文本、快速回复、列表消息和按钮。
  - 为常见消息模式（订单确认、支付请求）实现模板。
  - 处理消息格式限制（字符限制、按钮数量）。
  - 为订单摘要和餐厅信息提供丰富的格式。

- **WhatsApp 服务** (`whatsapp/services/whatsappService.js`):
  - 管理与 WhatsApp Business API 的通信。
  - 处理认证和令牌管理。
  - 提供发送不同消息类型的方法。
  - 实现速率限制和错误处理。
  - 维护具有重试逻辑的 API 客户端。

- **订单状态机操作** (`whatsapp/machines/orderFsmActions.js`):
  - 实现订单处理的业务逻辑。
  - 为对话管理器提供辅助功能。
  - 处理订单格式化、价格计算和验证。
  - 与 GraphQL API 接口以创建订单。
  - 管理货币配置和格式化。

##### ******* WhatsApp 集成测试点

为了测试 WhatsApp 集成，应考虑以下测试点：

1.  **Webhook 认证测试**:
    *   有效签名验证
    *   无效签名拒绝
    *   格式错误的签名处理
    *   缺少签名处理
2.  **Webhook 负载处理**:
    *   有效的消息接收事件
    *   有效的状态更新事件
    *   有效的对话状态更新事件
    *   格式错误的负载处理
    *   缺少必要字段的处理
    *   未知事件类型处理
3.  **会话管理测试**:
    *   新会话创建
    *   现有会话检索
    *   会话更新操作
    *   会话过期处理
    *   会话数据验证
    *   Redis 故障恢复
4.  **对话状态机测试**:
    *   从消息内容到事件的映射
    *   每种事件类型的状态转换
    *   状态转换后的上下文更新
    *   状态转换期间的错误处理
    *   事件队列处理
5.  **消息发送测试**:
    *   文本消息格式化
    *   交互式消息格式化
    *   消息模板渲染
    *   API 认证
    *   API 错误处理
    *   速率限制处理
6.  **购物车提交测试**:
    *   有效的购物车提交
    *   无效购物车数据处理
    *   会话令牌验证
    *   价格计算验证
    *   地址验证
    *   餐厅验证
7.  **订单处理测试**:
    *   通过 GraphQL 创建订单
    *   订单状态更新
    *   支付链接生成
    *   支付确认处理
    *   订单取消
8.  **集成测试方法**:
    *   模拟 WhatsApp Business API 以进行出站请求
    *   模拟 Webhook 事件以处理入站消息
    *   模拟 Redis 以进行会话存储
    *   模拟 MongoDB 以进行数据操作
    *   使用测试数据（fixtures）来处理餐厅和菜单数据
    *   实现端到端的对话流程

##### ******* WhatsApp 集成序列图

```
┌─────────┐          ┌─────────┐          ┌──────────┐          ┌──────────┐          ┌─────────┐
│WhatsApp │          │Webhook  │          │对话管理  │          │会话服务  │          │GraphQL  │
│Business │          │控制器   │          │器        │          │          │          │API      │
└────┬────┘          └────┬────┘          └────┬─────┘          └────┬─────┘          └────┬────┘
     │                     │                    │                     │                     │
     │  POST /webhook      │                    │                     │                     │
     │────────────────────>│                    │                     │                     │
     │                     │                    │                     │                     │
     │                     │  获取/创建会话     │                     │                     │
     │                     │────────────────────────────────────────>│                     │
     │                     │                    │                     │                     │
     │                     │  会话数据          │                     │                     │
     │                     │<────────────────────────────────────────│                     │
     │                     │                    │                     │                     │
     │                     │  处理事件          │                     │                     │
     │                     │───────────────────>│                     │                     │
     │                     │                    │                     │                     │
     │                     │                    │  更新会话           │                     │
     │                     │                    │────────────────────>│                     │
     │                     │                    │                     │                     │
     │                     │                    │  会话已更新         │                     │
     │                     │                    │<────────────────────│                     │
     │                     │                    │                     │                     │
     │                     │                    │  下单               │                     │
     │                     │                    │────────────────────────────────────────────>
     │                     │                    │                     │                     │
     │                     │                    │  订单已创建         │                     │
     │                     │                    │<────────────────────────────────────────────
     │                     │                    │                     │                     │
     │                     │                    │  更新会话           │                     │
     │                     │                    │────────────────────>│                     │
     │                     │                    │                     │                     │
     │  发送消息           │                    │                     │                     │
     │<─────────────────────────────────────────│                     │                     │
     │                     │                    │                     │                     │
```

#### 2.3.3 支付处理

支付处理系统与多个支付网关集成：

- **Stripe 集成**:
  - 通过 Stripe API 处理信用卡支付。
  - 为支付事件实现 Webhook 处理程序 (`routes/stripe.js`)。
  - 创建具有动态定价和元数据的结账会话。
  - 处理支付确认并更新订单状态。
  - 处理支付失败和退款。
  - 支持多种货币和支付方式。
  - 在数据库中存储交易记录以供对账。
  - 实现幂等性密钥以防止重复收费。

  **Stripe API 集成详情**:

  1.  **创建结账会话**:
      *   **端点**: `POST /stripe/create-checkout-session`
      *   **请求体**:
          ```json
          {
            "orderId": "order-id",
            "amount": 1000, // 以分为单位
            "currency": "usd",
            "customerEmail": "<EMAIL>",
            "metadata": {
              "orderId": "order-id",
              "customerId": "customer-id",
              "platform": "ws" // 用于WhatsApp订单
            }
          }
          ```
      *   **响应**:
          ```json
          {
            "id": "checkout-session-id",
            "url": "https://checkout.stripe.com/..."
          }
          ```

  2.  **Webhook 处理程序**:
      *   **端点**: `POST /stripe/webhook`
      *   **请求头 (Headers)**:
          *   `Stripe-Signature`: 来自 Stripe 的 HMAC 签名。
      *   **处理的事件**:
          *   `checkout.session.completed`: 支付成功。
          *   `checkout.session.expired`: 会话过期未支付。
          *   `payment_intent.succeeded`: 支付已确认。
          *   `payment_intent.payment_failed`: 支付失败。
      *   **处理逻辑**:
          *   验证 Webhook 签名。
          *   从元数据中提取订单 ID。
          *   在数据库中更新订单状态。
          *   对于 WhatsApp 订单，触发 `PAYMENT_COMPLETE` 事件。

  3.  **测试注意事项**:
      *   使用 Stripe 测试模式和测试 API 密钥。
      *   使用 Stripe CLI 模拟 Webhook 事件。
      *   为单元测试模拟 Stripe API 响应。
      *   使用重复的 Webhook 事件测试幂等性。
      *   验证支付事件后的订单状态更新。
      *   测试被拒支付的错误处理。
      *   验证元数据在整个支付流程中的保留。
- **Webhook 处理程序**: 处理支付状态更新。
- **交易管理**: 记录和验证支付交易。

#### 2.3.4 数据存储

系统使用 MongoDB 作为主数据存储，使用 Redis 进行缓存：

- **MongoDB**: 存储所有持久化数据，包括用户、餐厅、菜单和订单。
- **Redis**: 缓存会话数据、对话状态和频繁访问的信息。
- **数据模型**: 使用带验证的 Mongoose schema 构建。

#### 2.3.5 后台处理

后台任务使用 Bull 队列进行管理：

- **作业队列**: 处理异步任务，如通知和订单状态更新。
- **计划作业**: 处理定期任务，如数据清理和报告生成。

### 2.4 数据流

#### 2.4.1 通过 WhatsApp 的订单流

1.  **对话启动**:
    *   顾客通过 WhatsApp 发起对话。
    *   WhatsApp Business API 向 `/whatsapp/webhook` 端点发送 Webhook 事件。
    *   Webhook 控制器使用 `whatsappAuth` 中间件验证请求签名。
    *   系统从 Redis 创建新会话或检索现有会话。
2.  **会话管理**:
    *   会话使用顾客电话号码和品牌信息进行初始化。
    *   如果可用，从数据库中获取顾客信息。
    *   加载以前的订单历史以个性化体验。
    *   根据订单历史或品牌配置选择默认餐厅。
3.  **餐厅选择**:
    *   如果有多家餐厅可用，顾客选择首选餐厅。
    *   通过 `restaurantStore` 从缓存中检索餐厅数据。
    *   为网络订购生成带有会话令牌的菜单链接。
    *   将餐厅详情连同订购选项发送给顾客。
4.  **订单创建**:
    *   顾客通过带有会话令牌的菜单链接, 在网页端点提交购物车数据。
    *   购物车数据被提交到 `/whatsapp/submit-cart` 端点。
    *   购物车验证包括商品可用性、定价和配送限制。
    *   处理订单项目，包括附加项、规格和特殊说明。
5.  **地址管理**:
    *   系统检查是否需要配送地址（自取订单不需要）。
    *   对于配送订单，顾客从已保存的地址中选择或添加新地址。
    *   地址经过验证和地理编码，用于计算配送距离。
    *   根据距离或固定费率计算配送费用。
6.  **订单确认**:
    *   显示订单摘要，包含项目明细、税费和配送费。
    *   顾客通过交互式按钮或文本命令确认订单。
    *   通过 GraphQL mutation `placeOrderWhatsApp` 在数据库中创建订单。
    *   生成订单 ID 和参考编号，并存储在会话上下文中。
7.  **支付处理**:
    *   使用 Stripe Checkout 生成带有订单元数据的支付链接。
    *   通过 WhatsApp 将链接连同支付说明发送给顾客。
    *   顾客在 Stripe 托管页面上完成支付。
    *   Stripe Webhook 通知系统支付完成。
    *   在数据库和会话上下文中更新支付状态。
8.  **订单履行**:
    *   餐厅通过仪表板或通知系统接收订单通知。
    *   订单状态更新（已接受、准备中、已就绪）会发送给顾客。
    *   对于配送订单，提供骑手分配和跟踪信息。
    *   在适当的时候发送配送确认或取餐说明。
9.  **订单后互动**:
    *   发送订单完成消息，附带收据和反馈选项。
    *   顾客可以查看订单历史并从以前的订单中重新下单。
    *   会话保持活动状态以处理后续问题。
    *   为无缝的未来互动维护上下文。

### 2.5 状态管理

#### 2.5.1 会话管理

- **WhatsApp 会话**:
  - 存储在 Redis 中，具有可配置的 TTL（生存时间）。
  - 会话数据结构:
    ```javascript
    {
      id: String,              // 唯一的会话 ID (生成的 UUID)
      dialogueId: String,      // WhatsApp 对话 ID
      recipientId: String,     // WhatsApp 接收者 ID
      customerPhone: String,   // 顾客电话号码
      brandWhatsappId: String, // WhatsApp Business 账户 ID
      brandRef: Object,        // 品牌参考数据
      isActive: Boolean,       // 会话活动状态
      createdAt: String,       // ISO 时间戳
      updatedAt: String,       // ISO 时间戳
      context: {               // 对话上下文
        customer: Object,      // 顾客数据及订单历史
        selectedRestaurantRef: Object, // 选定的餐厅数据
        selectedAddressIndex: Number,  // 选定的地址索引
        isRestaurantSelected: Boolean, // 餐厅选择状态
        isAddressSelected: Boolean,    // 地址选择状态
        cartReceived: Boolean,         // 购物车状态
        currentOrder: Object,          // 当前订单数据
        currentOrderState: Object,     // 订单状态信息
        orderPlaced: Boolean,          // 下单状态
        paymentDone: Boolean,          // 支付完成状态
        completedOrders: Array,        // 以往完成的订单
        lastMessageReceived: String,   // 最后一条消息的时间戳
        lastMessageContent: String,    // 最后一条消息的内容
        lastMessageType: String        // 最后一条消息的类型
      }
    }
    ```
  - 会话初始化发生在 `webhookController.getOrCreateSession()` 中。
  - 会话检索使用以 dialogueId 为键的 Redis GET 操作。
  - 会话更新是原子操作，以防止竞争条件。
  - 会话过期由 Redis TTL 机制处理。
  - 用于处理 Redis 故障的会话恢复机制。

- **对话状态**:
  - 在 `dialog.js` 中使用状态机模式进行管理。
  - 状态转换由事件触发：
    - `MESSAGE_RECEIVED`: 收到顾客消息
    - `CART_SUBMITTED`: 通过网页界面提交了购物车数据
    - `PAYMENT_COMPLETE`: 收到支付确认
    - `RESTAURANT_SELECTED`: 餐厅选择事件
    - `ADDRESS_UPDATED`: 地址选择或更新事件
    - `ORDER_PLACED`: 订单确认事件
    - `PAYMENT_DONE`: 支付完成事件
    - `CHANGE_ADDRESS`: 地址更改请求
    - `CANCEL_ORDER`: 取消订单请求
    - `ORDER_HISTORY`: 订单历史请求
    - `REORDER`: 重新下单请求
  - 事件处理程序映射在 `eventHandlers` 对象中。
  - 通过 `messageToEventMap` 将消息内容映射到事件。
  - 在会话上下文中进行状态持久化。
  - 带有优雅降级的错误处理。

- **订单上下文**:
  - 在整个订购过程中在会话上下文中维护。
  - 关键组件：
    - `currentOrder`: 与 GraphQL schema 匹配的订单数据
    - `currentOrderState`: 附加的状态信息
    - `processedItems`: 包含完整详情的已处理订单项
    - 状态标志: `cartReceived`, `orderPlaced`, `paymentDone`
  - 订单完成或取消时重置上下文。
  - 用于 Redis 存储的上下文序列化。
  - 状态转换前的上下文验证。

#### 2.5.2 订单状态转换

订单通过多个状态进行，每个状态都有特定的触发器和验证：

- **PENDING (待处理)**:
  - 订单创建后的初始状态。
  - 触发条件: `ORDER_PLACED` 事件。
  - 必需字段: restaurantId, customerId, orderInput, orderAmount。
  - 存储位置: 数据库和会话上下文。
  - 验证: 商品可用性、定价、配送限制。
  - 下一状态: PAID, CANCELLED。

- **PAID (已支付)**:
  - 支付已确认但尚未被餐厅处理。
  - 触发条件: Stripe Webhook 或 `PAYMENT_DONE` 事件。
  - 必需字段: orderId, paymentMethod, paidAmount。
  - 验证: 支付金额与订单金额匹配。
  - 更新: 数据库中的订单记录，会话上下文。
  - 下一状态: ACCEPTED, CANCELLED。

- **ACCEPTED (已接单)**:
  - 餐厅已接受订单。
  - 触发条件: 餐厅仪表板操作。
  - 更新: 数据库中的订单状态。
  - 通知: 通过 WhatsApp 通知顾客。
  - 下一状态: PREPARING, CANCELLED。

- **PREPARING (准备中)**:
  - 食物准备中。
  - 触发条件: 餐厅仪表板操作。
  - 更新: 数据库中的订单状态、准备时间。
  - 通知: 通知顾客并提供预计时间。
  - 下一状态: READY, CANCELLED。

- **READY (已就绪)**:
  - 订单已准备好等待取餐或配送。
  - 触发条件: 餐厅仪表板操作。
  - 更新: 数据库中的订单状态。
  - 通知: 通知顾客和骑手。
  - 下一状态: PICKED (配送), COMPLETED (自取)。

- **PICKED (已取货)**:
  - 订单已被配送骑手取走。
  - 触发条件: 骑手应用操作。
  - 更新: 订单状态、骑手位置。
  - 通知: 通知顾客并提供跟踪信息。
  - 下一状态: DELIVERED, CANCELLED。

- **DELIVERED (已送达)**:
  - 订单成功送达。
  - 触发条件: 骑手应用操作或顾客确认。
  - 更新: 订单状态、送达时间。
  - 通知: 送达确认、请求反馈。
  - 下一状态: COMPLETED。

- **COMPLETED (已完成)**:
  - 订单完全完成并关闭。
  - 触发条件: 系统在送达或取餐后触发。
  - 更新: 订单状态、顾客订单历史。
  - 操作: 为新订单重置会话上下文。
  - 下一状态: 无 (最终状态)。

- **CANCELLED (已取消)**:
  - 订单被顾客、餐厅或系统取消。
  - 触发条件: `CANCEL_ORDER` 事件或仪表板操作。
  - 更新: 订单状态、取消原因。
  - 操作: 如果已支付则处理退款，重置会话上下文。
  - 通知: 向所有相关方发送取消通知。
  - 下一状态: 无 (最终状态)。

状态转换会被记录以供审计，并触发对所有相关方的适当通知。系统在每次转换时实施验证，以确保数据完整性和业务规则合规性。

### 2.6 安全架构

#### 2.6.1 认证与授权

- **基于 JWT 的认证**: 安全的令牌生成和验证。
- **基于角色的访问控制**: 为顾客、餐厅、骑手和管理员设置不同权限。
- **WhatsApp 认证**: Webhook 请求的签名验证。

#### 2.6.2 数据保护

- **输入验证**: GraphQL schema 验证和自定义验证器。
- **CORS 配置**: 受控的跨源资源共享。
- **敏感数据处理**: 对敏感信息进行加密。
- **速率限制**: 防止滥用和 DoS 攻击。

### 2.7 集成点

#### 2.7.1 外部服务

- **WhatsApp Business API**: 用于会话式订购。
- **Google Maps API**: 用于定位服务和距离计算。
- **支付网关**: Stripe 和 PayPal。
- **通知服务**: Firebase、SendGrid 和 Twilio。

#### 2.7.2 内部接口

- **GraphQL API**: 客户端应用程序的主要接口。
- **Webhook 端点**: 用于支付和消息回调。
- **WebSocket 订阅**: 用于实时更新。

### 2.8 可扩展性与性能

#### 2.8.1 缓存策略

- **餐厅数据**: 缓存在内存中以便快速访问。
- **会话数据**: 存储在 Redis 中，并设置适当的 TTL。
- **查询结果**: 在适当的情况下进行缓存，以减少数据库负载。

#### 2.8.2 水平扩展

- **无状态 API 设计**: 支持多个服务器实例。
- **Redis 用于共享状态**: 保持跨实例的一致性。
- **后台作业分发**: 异步任务的分布式处理。

### 2.9 监控与可观察性

#### 2.9.1 日志记录

- **结构化日志**: 使用 Winston 并采用 JSON 格式。
- **日志轮换**: 每日轮换以管理日志文件大小。
- **日志级别**: 为开发和生产环境设置不同级别。

#### 2.9.2 错误跟踪

- **Sentry 集成**: 实时错误监控和警报。
- **性能跟踪**: 跟踪慢操作和瓶颈。

### 2.10 部署架构

#### 2.10.1 环境配置

- **环境变量**: 通过 .env 文件进行配置。
- **特定环境设置**: 为开发、测试和生产环境提供不同配置。

#### 2.10.2 部署选项

- **云托管**: 与各种云平台兼容。
- **Render 平台**: 为 Render 部署提供特定优化。

### 2.11 未来考量

- **基于容器的部署**: 支持 Docker。
- **微服务迁移**: 有可能拆分为专门的服务。
- **GraphQL 联邦**: 分布式 GraphQL 实现。
- **国际化**: 支持多种语言和货币。
- **机器学习集成**: 用于订单推荐和欺诈检测。

## 3. 结论

Firespoon API 为食品配送平台提供了一套全面的后端解决方案，重点是通过 WhatsApp 集成实现会话式订购。该系统架构通过模块化设计和清晰的关注点分离，在性能、可扩展性和可维护性之间取得了平衡。以上概述的文档结构为开发者理解、使用和扩展该系统提供了一份有效的路线图。


# Firespoon API Documentation Structure Outline and System Architecture

## 1. Documentation Structure Outline

### 1.1 Introduction
- **Project Overview**: Firespoon API is a backend service for a food delivery platform
- **Purpose and Scope**: Provides GraphQL API for food ordering, restaurant management, and WhatsApp integration
- **Target Audience**: Developers, system administrators, and business stakeholders

### 1.2 Getting Started
- **Prerequisites**: Node.js, MongoDB, Redis
- **Installation**: Setup instructions and environment configuration
- **Configuration**: Environment variables and configuration files
- **Running the Application**: Development, testing, and production modes

### 1.3 Core Components
- **GraphQL API**: Schema, resolvers, and data models
- **Authentication System**: JWT-based authentication and authorization
- **WhatsApp Integration**: Webhook handling, dialog management, and order processing
- **Payment Processing**: Stripe and PayPal integration
- **Notification System**: Email, SMS, and push notifications

### 1.4 Data Models
- **User Management**: Customers, restaurant owners, riders, and administrators
- **Restaurant Management**: Restaurants, menus, categories, and food items
- **Order Processing**: Orders, order items, addons, and variations
- **Address and Location**: Customer addresses, restaurant locations, and delivery zones
- **Payment and Transactions**: Payment methods, transaction records, and earnings

### 1.5 API Reference
- **GraphQL Schema**: Types, queries, mutations, and subscriptions
- **REST Endpoints**: Payment webhooks and WhatsApp integration
- **Authentication**: Token generation and validation
- **Error Handling**: Error codes and response formats

### 1.6 WhatsApp Integration
- **Webhook Setup**: Configuration and authentication
- **Dialog Flow**: Conversation states and transitions
- **Order Processing**: Cart management and order placement
- **Payment Handling**: Payment confirmation and status updates
- **Session Management**: Customer session tracking and state persistence

### 1.7 Deployment
- **Environment Setup**: Development, staging, and production
- **Database Configuration**: MongoDB setup and indexing
- **Caching Strategy**: Redis implementation
- **Monitoring and Logging**: Sentry integration and Winston logging
- **Security Considerations**: Authentication, data protection, and CORS

### 1.8 Testing
- **Unit Testing**: Jest configuration and test coverage
- **Integration Testing**: API testing and webhook simulation
- **Performance Testing**: Load testing and benchmarking

### 1.9 Troubleshooting
- **Common Issues**: Known problems and solutions
- **Logging and Debugging**: Accessing and interpreting logs
- **Support Resources**: Contact information and community resources

## 2. System Architecture Document

### 2.1 System Overview

Firespoon API is a Node.js-based backend service that provides a GraphQL API for a food delivery platform. The system integrates with WhatsApp for conversational ordering, supports multiple payment gateways, and offers real-time order tracking. The architecture follows a modular design with clear separation of concerns.

### 2.2 Technology Stack

- **Runtime Environment**: Node.js
- **API Framework**: Express.js with Apollo Server (GraphQL)
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis for session management and data caching
- **Message Queue**: Bull for background job processing
- **Authentication**: JWT (JSON Web Tokens)
- **Payment Processing**: Stripe and PayPal
- **Messaging**: WhatsApp Business API (WABA)
  - Uses REST API for sending and receiving messages
  - Requires authentication via OAuth 2.0 tokens
  - Supports text, interactive messages, and media content
  - Webhook-based event notification system
  - Message templates for structured communication
- **Notifications**: Firebase Cloud Messaging, SendGrid (Email), Twilio (SMS)
- **Monitoring**: Sentry for error tracking and performance monitoring
- **Logging**: Winston with daily rotation
- **Testing**: Jest for unit and integration testing

### 2.3 System Components

#### 2.3.1 Core API Layer

The core API layer is built on Express.js and Apollo Server, providing a GraphQL interface for client applications. Key components include:

- **GraphQL Schema**: Defined in `.graphql` files under `graphql/schema/types/`
- **Resolvers**: Implemented in JavaScript files under `graphql/resolvers/`
- **Middleware**: Authentication and request processing in `middleware/`
- **Data Models**: Mongoose schemas in `models/`

#### 2.3.2 WhatsApp Integration

The WhatsApp integration enables conversational ordering through WhatsApp. Key components include:

##### ******* Webhook API Specifications

- **Webhook Endpoint**: `/whatsapp/webhook` (POST)
  - **Authentication**: HMAC signature validation via `whatsappAuth` middleware
  - **Headers**:
    - `X-WABA-Signature`: HMAC signature of request body
    - `Content-Type`: `application/vnd.api+json`

- **Webhook Request Payload Structure**:
  ```json
  {
    "data": {
      "type": "webhook_event",
      "id": "event-uuid",
      "attributes": {
        "type": "messages.received" | "messages.status_update" | "dialogues.status_update"
      }
    },
    "included": [
      {
        "type": "messages",
        "id": "message-id",
        "attributes": {
          "content": [
            {
              "displayType": "text",
              "attrs": {
                "text": "Message content"
              },
              "payload": {
                "externalID": "optional-external-id"
              }
            }
          ]
        }
      },
      {
        "type": "dialogues",
        "id": "dialogue-id",
        "attributes": {
          "status": "ACTIVE" | "CLOSED"
        },
        "relationships": {
          "recipient": {
            "data": {
              "id": "recipient-id",
              "type": "customers"
            }
          },
          "agent": {
            "data": {
              "id": "brand-whatsapp-id",
              "type": "agents"
            }
          }
        }
      }
    ]
  }
  ```

- **Webhook Response**:
  - Status code: `202 Accepted` (asynchronous processing)
  - Empty response body

##### ******* WhatsApp API Outbound Requests

- **Base URL**: Configured in environment variables
- **Authentication**: OAuth 2.0 Bearer token
- **Send Message Endpoint**: `POST /messages`
  - **Headers**:
    - `Authorization`: `Bearer {token}`
    - `Content-Type`: `application/vnd.api+json`
    - `Accept`: `application/vnd.api+json`

  - **Request Body for Text Message**:
    ```json
    {
      "data": {
        "type": "messages",
        "attributes": {
          "dialogueId": "dialogue-id",
          "content": [
            {
              "displayType": "text",
              "attrs": {
                "text": "Message text"
              }
            }
          ]
        }
      }
    }
    ```

  - **Request Body for Interactive Message**:
    ```json
    {
      "data": {
        "type": "messages",
        "attributes": {
          "dialogueId": "dialogue-id",
          "content": [
            {
              "displayType": "interactive",
              "attrs": {
                "type": "button",
                "header": "Header text",
                "body": "Message body",
                "footer": "Footer text",
                "buttons": [
                  {
                    "type": "reply",
                    "title": "Button text",
                    "payload": "button_payload"
                  }
                ]
              }
            }
          ]
        }
      }
    }
    ```

##### 2.3.2.3 Cart Submission API

- **Endpoint**: `/whatsapp/submit-cart` (POST)
  - **Authentication**: Session token validation via `sessionValidator` middleware
  - **Headers**:
    - `X-WhatsApp-Token`: Session token
    - `Content-Type`: `application/json`

  - **Request Body**:
    ```json
    {
      "restaurantId": "restaurant-id",
      "orderInput": [
        {
          "food": "food-id",
          "variation": "variation-id",
          "quantity": 1,
          "addons": [
            {
              "_id": "addon-id",
              "options": ["option-id-1", "option-id-2"]
            }
          ],
          "specialInstructions": "Optional instructions"
        }
      ],
      "isPickedUp": true|false,
      "deliveryAddressId": "address-id",
      "tipping": 0.00,
      "taxationAmount": 0.00,
      "instructions": "Order instructions"
    }
    ```

  - **Response**:
    - Success: `200 OK` with empty body
    - Error: `400 Bad Request` with error details

##### ******* Webhook Controller

- **Webhook Controller** (`whatsapp/controllers/webhookController.js`):
  - Handles incoming messages and events from WhatsApp Business API
  - Processes three main event types: `messages.received`, `messages.status_update`, and `dialogues.status_update`
  - Extracts message content, dialogue ID, and recipient information
  - Creates or retrieves session data for each conversation
  - Routes messages to the Dialog Manager for processing

- **Dialog Manager** (`whatsapp/machines/dialog.js`):
  - Implements a state machine approach for conversation flow
  - Manages 15+ distinct conversation states (e.g., restaurant selection, address selection, order confirmation)
  - Maps incoming messages to specific event types using `messageToEventMap`
  - Processes events through dedicated handlers (e.g., `handleCartUpdate`, `handleOrderPlaced`)
  - Maintains context throughout the conversation lifecycle

- **Session Service** (`whatsapp/services/sessionService.js`):
  - Maintains user session state in Redis with configurable TTL
  - Provides methods for creating, retrieving, updating, and deleting sessions
  - Implements session event queue for asynchronous processing
  - Handles session persistence and recovery
  - Stores conversation context, customer information, and order details

- **Restaurant Store** (`whatsapp/services/restaurantStore.js`):
  - Caches restaurant and menu data for quick access
  - Provides methods to retrieve restaurant and brand information by ID
  - Maintains in-memory cache with periodic refresh from database
  - Implements fallback mechanisms for cache misses
  - Optimizes menu data retrieval for WhatsApp ordering flow

- **Message Builders** (`whatsapp/services/messageBuilders.js`):
  - Constructs formatted messages for WhatsApp
  - Supports multiple message types: text, quick replies, list messages, and buttons
  - Implements templates for common message patterns (order confirmation, payment requests)
  - Handles message formatting constraints (character limits, button counts)
  - Provides rich formatting for order summaries and restaurant information

- **WhatsApp Service** (`whatsapp/services/whatsappService.js`):
  - Manages communication with WhatsApp Business API
  - Handles authentication and token management
  - Provides methods for sending different message types
  - Implements rate limiting and error handling
  - Maintains API client with retry logic

- **Order FSM Actions** (`whatsapp/machines/orderFsmActions.js`):
  - Implements business logic for order processing
  - Provides helper functions for the Dialog Manager
  - Handles order formatting, pricing calculations, and validation
  - Interfaces with the GraphQL API for order creation
  - Manages currency configuration and formatting

##### ******* WhatsApp Integration Test Points

For testing the WhatsApp integration, the following test points should be considered:

1. **Webhook Authentication Testing**:
   - Valid signature verification
   - Invalid signature rejection
   - Malformed signature handling
   - Missing signature handling

2. **Webhook Payload Processing**:
   - Valid message received event
   - Valid status update event
   - Valid dialogue status update event
   - Malformed payload handling
   - Missing required fields handling
   - Unknown event type handling

3. **Session Management Testing**:
   - New session creation
   - Existing session retrieval
   - Session update operations
   - Session expiration handling
   - Session data validation
   - Redis failure recovery

4. **Dialog State Machine Testing**:
   - Event mapping from message content
   - State transitions for each event type
   - Context updates after state transitions
   - Error handling during state transitions
   - Event queue processing

5. **Message Sending Testing**:
   - Text message formatting
   - Interactive message formatting
   - Message template rendering
   - API authentication
   - API error handling
   - Rate limiting handling

6. **Cart Submission Testing**:
   - Valid cart submission
   - Invalid cart data handling
   - Session token validation
   - Price calculation validation
   - Address validation
   - Restaurant validation

7. **Order Processing Testing**:
   - Order creation via GraphQL
   - Order status updates
   - Payment link generation
   - Payment confirmation handling
   - Order cancellation

8. **Integration Testing Approach**:
   - Mock WhatsApp Business API for outbound requests
   - Simulate webhook events for inbound messages
   - Mock Redis for session storage
   - Mock MongoDB for data operations
   - Use test fixtures for restaurant and menu data
   - Implement end-to-end conversation flows

##### ******* WhatsApp Integration Sequence Diagram

```
┌─────────┐          ┌─────────┐          ┌──────────┐          ┌──────────┐          ┌─────────┐
│WhatsApp │          │Webhook  │          │Dialog    │          │Session   │          │GraphQL  │
│Business │          │Controller│          │Manager   │          │Service   │          │API      │
└────┬────┘          └────┬────┘          └────┬─────┘          └────┬─────┘          └────┬────┘
     │                     │                    │                     │                     │
     │  POST /webhook      │                    │                     │                     │
     │────────────────────>│                    │                     │                     │
     │                     │                    │                     │                     │
     │                     │  Get/Create Session│                     │                     │
     │                     │────────────────────────────────────────>│                     │
     │                     │                    │                     │                     │
     │                     │  Session Data      │                     │                     │
     │                     │<────────────────────────────────────────│                     │
     │                     │                    │                     │                     │
     │                     │  Process Event     │                     │                     │
     │                     │───────────────────>│                     │                     │
     │                     │                    │                     │                     │
     │                     │                    │  Update Session     │                     │
     │                     │                    │────────────────────>│                     │
     │                     │                    │                     │                     │
     │                     │                    │  Session Updated    │                     │
     │                     │                    │<────────────────────│                     │
     │                     │                    │                     │                     │
     │                     │                    │  Place Order        │                     │
     │                     │                    │────────────────────────────────────────────>
     │                     │                    │                     │                     │
     │                     │                    │  Order Created      │                     │
     │                     │                    │<────────────────────────────────────────────
     │                     │                    │                     │                     │
     │                     │                    │  Update Session     │                     │
     │                     │                    │────────────────────>│                     │
     │                     │                    │                     │                     │
     │  Send Message       │                    │                     │                     │
     │<─────────────────────────────────────────│                     │                     │
     │                     │                    │                     │                     │
```

#### 2.3.3 Payment Processing

The payment processing system integrates with multiple payment gateways:

- **Stripe Integration**:
  - Handles credit card payments through Stripe API
  - Implements webhook handler for payment events (`routes/stripe.js`)
  - Creates checkout sessions with dynamic pricing and metadata
  - Processes payment confirmations and updates order status
  - Handles payment failures and refunds
  - Supports multiple currencies and payment methods
  - Stores transaction records in database for reconciliation
  - Implements idempotency keys to prevent duplicate charges

  **Stripe API Integration Details**:

  1. **Checkout Session Creation**:
     - **Endpoint**: `POST /stripe/create-checkout-session`
     - **Request Body**:
       ```json
       {
         "orderId": "order-id",
         "amount": 1000, // in cents
         "currency": "usd",
         "customerEmail": "<EMAIL>",
         "metadata": {
           "orderId": "order-id",
           "customerId": "customer-id",
           "platform": "ws" // for WhatsApp orders
         }
       }
       ```
     - **Response**:
       ```json
       {
         "id": "checkout-session-id",
         "url": "https://checkout.stripe.com/..."
       }
       ```

  2. **Webhook Handler**:
     - **Endpoint**: `POST /stripe/webhook`
     - **Headers**:
       - `Stripe-Signature`: HMAC signature from Stripe
     - **Events Handled**:
       - `checkout.session.completed`: Payment successful
       - `checkout.session.expired`: Session expired without payment
       - `payment_intent.succeeded`: Payment confirmed
       - `payment_intent.payment_failed`: Payment failed
     - **Processing Logic**:
       - Verify webhook signature
       - Extract order ID from metadata
       - Update order status in database
       - For WhatsApp orders, trigger `PAYMENT_COMPLETE` event

  3. **Testing Considerations**:
     - Use Stripe test mode and test API keys
     - Simulate webhook events with Stripe CLI
     - Mock Stripe API responses for unit tests
     - Test idempotency with duplicate webhook events
     - Verify order status updates after payment events
     - Test error handling for declined payments
     - Validate metadata preservation throughout payment flow
- **PayPal Integration**: Processes PayPal payments
- **Webhook Handlers**: Processes payment status updates
- **Transaction Management**: Records and validates payment transactions

#### 2.3.4 Data Storage

The system uses MongoDB as the primary data store with Redis for caching:

- **MongoDB**: Stores all persistent data including users, restaurants, menus, and orders
- **Redis**: Caches session data, dialog state, and frequently accessed information
- **Data Models**: Structured using Mongoose schemas with validation

#### 2.3.5 Background Processing

Background tasks are managed using Bull queue:

- **Job Queue**: Processes asynchronous tasks like notifications and order status updates
- **Scheduled Jobs**: Handles recurring tasks like data cleanup and report generation

### 2.4 Data Flow

#### 2.4.1 Order Flow via GraphQL API

1. Client authenticates and receives JWT token
2. Client submits order through GraphQL mutation
3. Server validates order and calculates pricing
4. Payment is processed through selected gateway
5. Order is saved to database and notifications are sent
6. Real-time updates are delivered via GraphQL subscriptions

#### 2.4.2 Order Flow via WhatsApp

1. **Conversation Initiation**:
   - Customer initiates conversation through WhatsApp
   - WhatsApp Business API sends webhook event to `/whatsapp/webhook` endpoint
   - Webhook controller validates the request signature using `whatsappAuth` middleware
   - System creates a new session or retrieves existing session from Redis

2. **Session Management**:
   - Session is initialized with customer phone number and brand information
   - Customer information is fetched from database if available
   - Previous order history is loaded to personalize the experience
   - Default restaurant is selected based on order history or brand configuration

3. **Restaurant Selection**:
   - If multiple restaurants are available, customer selects preferred restaurant
   - Restaurant data is retrieved from cache via `restaurantStore`
   - Menu link is generated with session token for web ordering
   - Restaurant details are sent to customer with ordering options

4. **Order Creation**:
   - Customer places order via web interface or direct WhatsApp interaction
   - For web orders, cart data is submitted to `/whatsapp/submit-cart` endpoint
   - Cart validation includes item availability, pricing, and delivery constraints
   - Order items are processed with addons, variations, and special instructions

5. **Address Management**:
   - System checks if delivery address is required (not needed for pickup orders)
   - For delivery orders, customer selects from saved addresses or adds new address
   - Address is validated and geocoded for delivery distance calculation
   - Delivery charges are calculated based on distance or fixed rates

6. **Order Confirmation**:
   - Order summary is presented with itemized pricing, taxes, and delivery fees
   - Customer confirms order via interactive button or text command
   - Order is created in database via GraphQL mutation `placeOrderWhatsApp`
   - Order ID and reference number are generated and stored in session context

7. **Payment Processing**:
   - Payment link is generated using Stripe Checkout with order metadata
   - Link is sent to customer via WhatsApp with payment instructions
   - Customer completes payment on Stripe hosted page
   - Stripe webhook notifies system of payment completion
   - Payment status is updated in database and session context

8. **Order Fulfillment**:
   - Restaurant receives order notification via dashboard or notification system
   - Order status updates (accepted, preparing, ready) are sent to customer
   - For delivery orders, rider assignment and tracking information is provided
   - Delivery confirmation or pickup instructions are sent at appropriate times

9. **Post-Order Interaction**:
   - Order completion message is sent with receipt and feedback options
   - Customer can view order history and reorder from previous orders
   - Session remains active for follow-up questions or issues
   - Context is maintained for seamless future interactions

### 2.5 State Management

#### 2.5.1 Session Management

- **WhatsApp Sessions**:
  - Stored in Redis with configurable TTL (Time To Live)
  - Session data structure:
    ```javascript
    {
      id: String,              // Unique session ID (generated UUID)
      dialogueId: String,      // WhatsApp dialogue ID
      recipientId: String,     // WhatsApp recipient ID
      customerPhone: String,   // Customer phone number
      brandWhatsappId: String, // WhatsApp Business Account ID
      brandRef: Object,        // Brand reference data
      isActive: Boolean,       // Session active status
      createdAt: String,       // ISO timestamp
      updatedAt: String,       // ISO timestamp
      context: {               // Conversation context
        customer: Object,      // Customer data with order history
        selectedRestaurantRef: Object, // Selected restaurant data
        selectedAddressIndex: Number,  // Index of selected address
        isRestaurantSelected: Boolean, // Restaurant selection status
        isAddressSelected: Boolean,    // Address selection status
        cartReceived: Boolean,         // Cart status
        currentOrder: Object,          // Current order data
        currentOrderState: Object,     // Order state information
        orderPlaced: Boolean,          // Order placement status
        paymentDone: Boolean,          // Payment completion status
        completedOrders: Array,        // Previously completed orders
        lastMessageReceived: String,   // Last message timestamp
        lastMessageContent: String,    // Last message content
        lastMessageType: String        // Last message type
      }
    }
    ```
  - Session initialization occurs in `webhookController.getOrCreateSession()`
  - Session retrieval uses Redis GET operations with dialogueId as key
  - Session updates are atomic operations to prevent race conditions
  - Session expiration handled by Redis TTL mechanism
  - Session recovery mechanisms for handling Redis failures

- **Dialog State**:
  - Managed using a state machine pattern in `dialog.js`
  - State transitions triggered by events:
    - `MESSAGE_RECEIVED`: Customer message received
    - `CART_SUBMITTED`: Cart data submitted via web interface
    - `PAYMENT_COMPLETE`: Payment confirmation received
    - `RESTAURANT_SELECTED`: Restaurant selection event
    - `ADDRESS_UPDATED`: Address selection or update event
    - `ORDER_PLACED`: Order confirmation event
    - `PAYMENT_DONE`: Payment completion event
    - `CHANGE_ADDRESS`: Address change request
    - `CANCEL_ORDER`: Order cancellation request
    - `ORDER_HISTORY`: Order history request
    - `REORDER`: Reorder request
  - Event handlers mapped in `eventHandlers` object
  - Message content mapped to events via `messageToEventMap`
  - State persistence in session context
  - Error handling with graceful degradation

- **Order Context**:
  - Maintained throughout the ordering process in session context
  - Key components:
    - `currentOrder`: Order data matching GraphQL schema
    - `currentOrderState`: Additional state information
    - `processedItems`: Processed order items with full details
    - Status flags: `cartReceived`, `orderPlaced`, `paymentDone`
  - Context reset on order completion or cancellation
  - Context serialization for Redis storage
  - Context validation before state transitions

#### 2.5.2 Order State Transitions

Orders progress through multiple states with specific triggers and validations:

- **PENDING**:
  - Initial state after order creation
  - Triggered by: `ORDER_PLACED` event
  - Required fields: restaurantId, customerId, orderInput, orderAmount
  - Stored in: database and session context
  - Validation: Item availability, pricing, delivery constraints
  - Next states: PAID, CANCELLED

- **PAID**:
  - Payment confirmed but not yet processed by restaurant
  - Triggered by: Stripe webhook or `PAYMENT_DONE` event
  - Required fields: orderId, paymentMethod, paidAmount
  - Validation: Payment amount matches order amount
  - Updates: Order record in database, session context
  - Next states: ACCEPTED, CANCELLED

- **ACCEPTED**:
  - Restaurant has accepted the order
  - Triggered by: Restaurant dashboard action
  - Updates: Order status in database
  - Notifications: Customer notification via WhatsApp
  - Next states: PREPARING, CANCELLED

- **PREPARING**:
  - Food preparation in progress
  - Triggered by: Restaurant dashboard action
  - Updates: Order status in database, preparation time
  - Notifications: Customer notification with estimated time
  - Next states: READY, CANCELLED

- **READY**:
  - Order ready for pickup or delivery
  - Triggered by: Restaurant dashboard action
  - Updates: Order status in database
  - Notifications: Customer and rider notifications
  - Next states: PICKED (for delivery), COMPLETED (for pickup)

- **PICKED**:
  - Order picked up by delivery rider
  - Triggered by: Rider app action
  - Updates: Order status, rider location
  - Notifications: Customer notification with tracking info
  - Next states: DELIVERED, CANCELLED

- **DELIVERED**:
  - Order successfully delivered
  - Triggered by: Rider app action or customer confirmation
  - Updates: Order status, delivery time
  - Notifications: Delivery confirmation, feedback request
  - Next states: COMPLETED

- **COMPLETED**:
  - Order fully completed and closed
  - Triggered by: System after delivery or pickup
  - Updates: Order status, customer order history
  - Actions: Session context reset for new orders
  - Next states: None (terminal state)

- **CANCELLED**:
  - Order cancelled by customer, restaurant, or system
  - Triggered by: `CANCEL_ORDER` event or dashboard action
  - Updates: Order status, cancellation reason
  - Actions: Refund processing if paid, session context reset
  - Notifications: Cancellation notification to all parties
  - Next states: None (terminal state)

State transitions are logged for audit purposes and trigger appropriate notifications to all involved parties. The system implements validation at each transition to ensure data integrity and business rule compliance.

### 2.6 Security Architecture

#### 2.6.1 Authentication and Authorization

- **JWT-based Authentication**: Secure token generation and validation
- **Role-based Access Control**: Different permissions for customers, restaurants, riders, and admins
- **WhatsApp Authentication**: Signature validation for webhook requests

#### 2.6.2 Data Protection

- **Input Validation**: GraphQL schema validation and custom validators
- **CORS Configuration**: Controlled cross-origin resource sharing
- **Sensitive Data Handling**: Encryption of sensitive information
- **Rate Limiting**: Protection against abuse and DoS attacks

### 2.7 Integration Points

#### 2.7.1 External Services

- **WhatsApp Business API**: For conversational ordering
- **Google Maps API**: For location services and distance calculation
- **Payment Gateways**: Stripe and PayPal
- **Notification Services**: Firebase, SendGrid, and Twilio

#### 2.7.2 Internal Interfaces

- **GraphQL API**: Primary interface for client applications
- **Webhook Endpoints**: For payment and messaging callbacks
- **WebSocket Subscriptions**: For real-time updates

### 2.8 Scalability and Performance

#### 2.8.1 Caching Strategy

- **Restaurant Data**: Cached in memory for quick access
- **Session Data**: Stored in Redis with appropriate TTL
- **Query Results**: Cached where appropriate to reduce database load

#### 2.8.2 Horizontal Scaling

- **Stateless API Design**: Enables multiple server instances
- **Redis for Shared State**: Maintains consistency across instances
- **Background Job Distribution**: Distributed processing of asynchronous tasks

### 2.9 Monitoring and Observability

#### 2.9.1 Logging

- **Structured Logging**: Using Winston with JSON format
- **Log Rotation**: Daily rotation to manage log file size
- **Log Levels**: Different levels for development and production

#### 2.9.2 Error Tracking

- **Sentry Integration**: Real-time error monitoring and alerting
- **Performance Tracking**: Tracing for slow operations and bottlenecks

### 2.10 Deployment Architecture

#### 2.10.1 Environment Configuration

- **Environment Variables**: Configuration through .env files
- **Environment-specific Settings**: Different configurations for development, testing, and production

#### 2.10.2 Deployment Options

- **Container-based Deployment**: Docker support
- **Cloud Hosting**: Compatible with various cloud platforms
- **Render Platform**: Specific optimizations for Render deployment

### 2.11 Future Considerations

- **Microservices Migration**: Potential to split into specialized services
- **GraphQL Federation**: Distributed GraphQL implementation
- **Internationalization**: Support for multiple languages and currencies
- **Machine Learning Integration**: For order recommendations and fraud detection

## 3. Conclusion

The Firespoon API provides a comprehensive backend solution for food delivery platforms with a focus on WhatsApp integration for conversational ordering. The system architecture balances performance, scalability, and maintainability through a modular design and clear separation of concerns. The documentation structure outlined above provides a roadmap for developers to understand, use, and extend the system effectively.
