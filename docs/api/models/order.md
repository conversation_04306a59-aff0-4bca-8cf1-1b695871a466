# Order Model Documentation

## Overview

The Order model represents customer orders in the Firespoon system. It includes comprehensive order information, payment details, delivery information, and refund status tracking.

## Schema Definition

### Basic Order Information
- `orderId`: String (required) - Unique order identifier
- `orderAmount`: Number (required) - Total order amount
- `restaurantId`: String (required) - Restaurant identifier
- `restaurantName`: String (required) - Restaurant name
- `restaurantBrand`: String (required) - Restaurant brand name
- `restaurantBrandId`: ObjectId (required) - Reference to Brand model

### Customer Information
- `customerId`: String (required) - Customer identifier
- `customerPhone`: String - Customer phone number

### Delivery Information
- `deliveryAddress`: String (required) - Delivery address
- `deliveryAddressId`: String (required) - Address identifier
- `deliveryCoordinates`: Point - GPS coordinates for delivery
- `deliveryInstructions`: String - Special delivery instructions
- `deliveryCharges`: Number - Delivery fee amount

### Order Items
- `items`: Array of ItemSchema - Ordered items with variations and addons

### Payment Information
- `paymentStatus`: String (enum) - Payment status (PENDING, PAID, FAILED, etc.)
- `paymentMethod`: String (enum) - Payment method (STRIPE, PAYPAL, CASH, etc.)
- `paidAmount`: Number - Amount actually paid
- `taxationAmount`: Number - Tax amount (default: 0)
- `tipping`: Number - Tip amount (default: 0)

### Order Status and Tracking
- `orderStatus`: String (enum) - Current order status
- `isPickedUp`: Boolean (default: false) - Whether order is for pickup
- `reason`: String - Cancellation or other reason

### Refund Information

#### refundStatus
- **Type**: String
- **Enum Values**: ['NONE', 'PARTIAL', 'FULL']
- **Default**: 'NONE'
- **Description**: Indicates the refund status of the order
  - `NONE`: No refunds have been processed
  - `PARTIAL`: Some amount has been refunded
  - `FULL`: Full order amount has been refunded

#### totalRefunded
- **Type**: Number
- **Default**: 0
- **Min**: 0
- **Description**: Total amount refunded for this order

#### refunds
- **Type**: Array of ObjectId
- **Reference**: 'Refund'
- **Description**: Array of refund record references

### Timestamps and Dates
- `orderDate`: Date (default: Date.now()) - Order creation date
- `expectedTime`: Date - Expected completion time
- `preparationTime`: Date - Food preparation time
- `acceptedAt`: Date - When restaurant accepted the order
- `pickedAt`: Date - When order was picked up
- `deliveredAt`: Date - When order was delivered
- `cancelledAt`: Date - When order was cancelled
- `assignedAt`: Date - When rider was assigned
- `completionTime`: Date - Actual completion time

### Additional Features
- `coupon`: CouponSchema - Applied coupon information
- `review`: ObjectId - Reference to Review model
- `rider`: ObjectId - Reference to Rider model
- `zone`: ObjectId - Reference to Zone model
- `chat`: Array of MessageSchema - Order chat messages
- `isActive`: Boolean (default: true) - Whether order is active
- `isRinged`: Boolean (default: true) - Notification status
- `isRiderRinged`: Boolean (default: true) - Rider notification status
- `instructions`: String - Special instructions

## Usage Examples

### Creating a New Order
```javascript
const order = new Order({
  orderId: 'ORD-2024-001',
  orderAmount: 25.99,
  restaurantId: 'rest_123',
  restaurantName: 'Pizza Palace',
  restaurantBrand: 'Palace Group',
  restaurantBrandId: new mongoose.Types.ObjectId(),
  customerId: 'cust_456',
  customerPhone: '+1234567890',
  deliveryAddress: '123 Main St, City',
  deliveryAddressId: 'addr_789',
  items: [/* item objects */],
  orderStatus: 'PENDING',
  paymentMethod: 'STRIPE',
  paymentStatus: 'PAID',
  paidAmount: 25.99
  // refundStatus will default to 'NONE'
  // totalRefunded will default to 0
  // refunds will default to []
});
```

### Checking Refund Status
```javascript
// Check if order has any refunds
if (order.refundStatus === 'NONE') {
  console.log('No refunds processed');
} else if (order.refundStatus === 'PARTIAL') {
  console.log(`Partial refund: $${order.totalRefunded}`);
} else if (order.refundStatus === 'FULL') {
  console.log('Full refund processed');
}
```

### Updating Refund Information
```javascript
// After processing a refund
order.refunds.push(refundId);
order.totalRefunded += refundAmount;
order.refundStatus = order.totalRefunded >= order.orderAmount ? 'FULL' : 'PARTIAL';
await order.save();
```

## Validation Rules

1. **Required Fields**: orderId, orderAmount, restaurantId, restaurantName, restaurantBrand, restaurantBrandId, customerId, deliveryAddress, deliveryAddressId, orderStatus
2. **Enum Validation**: paymentStatus, paymentMethod, orderStatus, refundStatus must match defined enum values
3. **Number Validation**: totalRefunded must be >= 0
4. **Reference Validation**: ObjectId fields must reference valid documents

## Indexes

Recommended indexes for optimal performance:
- `orderId` (unique)
- `customerId`
- `restaurantId`
- `orderStatus`
- `refundStatus`
- `orderDate`

## Related Models

- **Refund**: Referenced by `refunds` array
- **Restaurant**: Referenced by `restaurantId`
- **Customer**: Referenced by `customerId`
- **Rider**: Referenced by `rider`
- **Review**: Referenced by `review`
- **Zone**: Referenced by `zone`

## GraphQL Integration

The Order model is exposed through GraphQL with the following types:
- `Order`: Main order type
- `OrderRefundStatus`: Enum for refund status values
- `OrderStatus`: Enum for order status values
- `PaymentStatus`: Enum for payment status values
