# Firespoon API Documentation

## Overview

Firespoon API provides a GraphQL-based interface for the food delivery platform. The API supports restaurant management, order processing, customer management, and WhatsApp integration.

## GraphQL Schema

The complete GraphQL schema is available in the source code: `graphql/schema` directory.  That directory is used to generate code. So so GraphQL API related document in docs diretory.
Key schema files include:

### Core Types
- `base.graphql` - Base types and scalars
- `extra.graphql` - Extended queries and mutations

### Entity Types
- `restaurant.graphql` - Restaurant management
- `order.graphql` - Order processing
- `customer.graphql` - Customer management
- `food.graphql` - Food items and menus
- `rider.graphql` - Delivery rider management
- `session.graphql` - WhatsApp session management

## API Endpoints

### GraphQL Endpoint
- **URL**: `/graphql`
- **Method**: POST
- **Content-Type**: `application/json`

### Development Tools
- **GraphQL Playground**: Available at `/graphql` in development mode
- **GraphQL Voyager**: Available for schema visualization

## Authentication

The API uses JWT-based authentication with custom middleware:
- Header: `Authorization: Bearer <token>`
- WhatsApp integration uses separate authentication

## Key Features

1. **Restaurant Management**: CRUD operations for restaurants, menus, and food items
2. **Order Processing**: Complete order lifecycle management
3. **Customer Management**: User registration, authentication, and profiles
4. **Payment Integration**: Stripe and PayPal support
5. **WhatsApp Integration**: Conversational ordering through WhatsApp
6. **Real-time Updates**: GraphQL subscriptions for order tracking

## Usage Examples

### Query Example
```graphql
query GetRestaurants {
  restaurants {
    _id
    name
    address
    isActive
    foods {
      _id
      title
      price
    }
  }
}
```

### Mutation Example
```graphql
mutation CreateOrder($orderInput: OrderInput!) {
  createOrder(orderInput: $orderInput) {
    _id
    orderId
    orderStatus
    orderAmount
  }
}
```

## Error Handling

The API returns standard GraphQL errors with additional context:
- Authentication errors
- Validation errors
- Business logic errors
- System errors

## Rate Limiting

Rate limiting is implemented to prevent abuse:
- Standard queries: 100 requests per minute
- Mutations: 50 requests per minute
- Authentication endpoints: 10 requests per minute

## Development

For development setup and testing, see the main project documentation in `docs/requirements/`.
