# 安全配置迁移指南

本指南将帮助您将现有的Firespoon API从硬编码密钥迁移到安全的环境变量配置。

## 概述

此次安全重构解决了两个主要安全问题：
1. **Firebase服务账户密钥暴露**：`serviceAccountKey.json` 文件包含私钥直接提交到代码库
2. **JWT密钥硬编码**：多处使用 `'somesupersecretkey'` 作为JWT签名密钥

## 迁移步骤

### 1. 本地开发环境迁移

#### 步骤 1.1：生成JWT密钥
```bash
# 生成强随机JWT密钥
openssl rand -base64 32
```

#### 步骤 1.2：配置环境变量
```bash
# 复制环境变量模板
cp .env.local.example .env.local

# 编辑配置文件
nano .env.local
```

在 `.env.local` 中设置：
```bash
# 使用步骤1.1生成的密钥
JWT_SECRET=your-generated-jwt-secret-here

# Firebase配置（二选一）
# 选项1：使用环境变量（推荐）
FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-project",...}'

# 选项2：继续使用本地文件（开发环境）
# 将serviceAccountKey.json放在项目根目录（会显示警告）
```

#### 步骤 1.3：获取Firebase服务账户密钥
1. 访问 [Firebase Console](https://console.firebase.google.com/)
2. 选择您的项目
3. 进入 "项目设置" > "服务账户"
4. 点击 "生成新的私钥" 下载JSON文件
5. 将JSON内容作为字符串设置到 `FIREBASE_SERVICE_ACCOUNT_KEY` 环境变量

### 2. Render.com 生产环境配置

#### 步骤 2.1：设置环境变量
1. 登录 Render.com 控制台
2. 选择您的服务
3. 进入 "Environment" 标签页
4. 添加以下环境变量：

```bash
NODE_ENV=render
JWT_SECRET=your-production-jwt-secret
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project",...}
```

**重要提示**：
- 生产环境的JWT_SECRET必须与开发环境不同
- FIREBASE_SERVICE_ACCOUNT_KEY必须是完整的JSON字符串
- 确保所有其他必需的环境变量都已设置

#### 步骤 2.2：验证部署
部署后检查日志确认：
- 没有关于缺失环境变量的错误
- Firebase初始化成功
- JWT认证正常工作

### 3. 测试环境配置

```bash
# 复制测试环境模板
cp .env.test.example .env.test

# 编辑测试配置
nano .env.test
```

测试环境使用Testcontainers管理数据库，只需配置：
- JWT_SECRET
- FIREBASE_SERVICE_ACCOUNT_KEY（可选，测试时可能不需要Firebase功能）

## 安全最佳实践

### 1. 密钥管理
- **永远不要**将真实的密钥提交到版本控制
- 为每个环境使用不同的JWT密钥
- 定期轮换生产环境密钥
- 使用强随机密钥（至少32字节）

### 2. 环境隔离
- 开发、测试、生产环境使用完全不同的凭据
- 生产环境凭据只有必要人员可访问
- 使用环境变量而不是配置文件存储敏感信息

### 3. Firebase安全
- 为每个环境创建独立的Firebase项目
- 定期审查Firebase IAM权限
- 监控Firebase使用情况和异常访问

## 故障排除

### 常见错误及解决方案

#### 错误：`Missing required environment variables: JWT_SECRET`
**解决方案**：确保在环境变量中设置了JWT_SECRET

#### 错误：`Invalid FIREBASE_SERVICE_ACCOUNT_KEY format`
**解决方案**：
1. 确保JSON格式正确
2. 检查是否有多余的引号或转义字符
3. 验证JSON包含所有必需字段

#### 错误：`Firebase credentials not found`
**解决方案**：
1. 检查FIREBASE_SERVICE_ACCOUNT_KEY环境变量
2. 在开发环境中，确保serviceAccountKey.json文件存在
3. 验证Firebase项目ID和权限

#### 警告：`Using Firebase credentials from local file`
这是正常的开发环境警告，提醒您迁移到环境变量。在生产环境中不会出现此警告。

### 验证配置

运行以下命令验证配置：
```bash
# 检查环境变量
node -e "console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'SET' : 'NOT SET')"
node -e "console.log('FIREBASE_SERVICE_ACCOUNT_KEY:', process.env.FIREBASE_SERVICE_ACCOUNT_KEY ? 'SET' : 'NOT SET')"

# 启动应用并检查日志
npm start
```

## 回滚计划

如果迁移过程中遇到问题，可以临时回滚：

1. **保留原始文件**：在迁移前备份 `serviceAccountKey.json`
2. **代码回滚**：如果需要，可以临时恢复硬编码密钥
3. **逐步迁移**：可以先迁移JWT密钥，再迁移Firebase配置

## Restaurant App配置

### 重要提醒
Restaurant App需要与API服务器使用相同的JWT密钥进行认证：

#### 如果Restaurant App是独立应用：
1. **Restaurant App不需要知道JWT_SECRET**
2. **只需要确保**：
   - 正确的API endpoint配置
   - 调用 `restaurantLogin` mutation获取token
   - 在Authorization header中发送token: `Bearer <token>`

#### 如果Restaurant App有JWT验证功能：
1. **必须使用相同的JWT_SECRET环境变量**
2. **在Restaurant App的环境配置中设置**：
   ```bash
   JWT_SECRET=与API服务器相同的密钥
   ```

#### Restaurant App认证流程：
```
1. Restaurant App → restaurantLogin(username, password)
2. API返回 → { token, restaurantId }
3. Restaurant App存储token
4. 后续请求 → Authorization: Bearer <token>
5. API验证token → 允许访问
```

## 支持

如果在迁移过程中遇到问题：
1. 检查应用日志中的详细错误信息
2. 验证所有环境变量都已正确设置
3. 确认Firebase项目配置和权限
4. **特别检查Restaurant App的JWT token处理**
5. 联系开发团队获取支持
