# ADR-002: WhatsApp Integration Architecture

## Status
Accepted

## Context
The Firespoon platform needed to support conversational ordering through WhatsApp to reach customers who prefer messaging over mobile apps. Requirements included:

1. **Conversational Ordering**: Complete order flow through WhatsApp chat
2. **Session Management**: Maintain conversation state across messages
3. **Menu Navigation**: Browse restaurants and food items via chat
4. **Order Processing**: Place orders and handle payments through WhatsApp
5. **Real-time Updates**: Order status notifications via WhatsApp

## Decision
We decided to implement WhatsApp integration using a state machine approach with Payemoji as the WhatsApp Business API provider.

### Key Components:
- **Payemoji Integration**: Third-party WhatsApp Business API provider
- **State Machine Dialog Manager**: Manages conversation flow and states
- **Session Service**: Redis-based session storage for conversation context
- **Webhook Controller**: Handles incoming WhatsApp messages and events
- **Message Builders**: Constructs formatted WhatsApp messages

### Architecture Pattern:
- **Event-Driven**: Webhook-based message processing
- **State Machine**: Finite state machine for dialog management
- **Session-Based**: Stateful conversations with Redis persistence

## Consequences

### Positive:
1. **Scalable Conversation Management**: State machine handles complex dialog flows
2. **Persistent Sessions**: Redis ensures conversation continuity
3. **Flexible Message Types**: Support for text, buttons, lists, and interactive messages
4. **Reliable Delivery**: Webhook-based event processing ensures message delivery
5. **Business Logic Separation**: Clear separation between dialog flow and business logic

### Negative:
1. **Third-party Dependency**: Reliance on Payemoji service availability
2. **State Complexity**: Complex state transitions require careful testing
3. **Session Storage**: Redis dependency for session persistence
4. **Message Formatting**: WhatsApp message format constraints

### Mitigation Strategies:
1. **Error Handling**: Comprehensive error handling for API failures
2. **Session Recovery**: Fallback mechanisms for session data loss
3. **Rate Limiting**: Respect WhatsApp API rate limits
4. **Testing Strategy**: Extensive testing of state transitions and message flows

## Implementation Details

### State Machine States:
```
INITIAL → RESTAURANT_SELECTION → MENU_BROWSING → CART_MANAGEMENT → 
ADDRESS_SELECTION → ORDER_CONFIRMATION → PAYMENT_PROCESSING → ORDER_PLACED
```

### Session Data Structure:
```javascript
{
  restaurantId: String,
  customerId: String,
  orderInput: Array,
  paymentMethod: String,
  deliveryAddressId: String,
  conversationState: String,
  lastActivity: Date
}
```

### Message Flow:
1. **Incoming Message**: Webhook receives message from Payemoji
2. **Session Retrieval**: Get or create session for conversation
3. **Event Processing**: Map message to dialog event
4. **State Transition**: Update conversation state
5. **Response Generation**: Build appropriate response message
6. **Message Sending**: Send response via Payemoji API

### API Integration:
- **Inbound**: Webhook endpoint for receiving messages
- **Outbound**: REST API calls to Payemoji for sending messages
- **Authentication**: OAuth 2.0 token-based authentication
- **Message Types**: Text, interactive buttons, list messages

### Testing Approach:
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: End-to-end conversation flows
3. **Mock Services**: Payemoji API mocking for testing
4. **State Validation**: Verify state transitions and data persistence

## Security Considerations
1. **Webhook Validation**: HMAC signature verification for incoming webhooks
2. **Session Security**: Secure session token generation and validation
3. **Data Privacy**: Customer data protection in session storage
4. **API Authentication**: Secure token management for Payemoji API

## Performance Considerations
1. **Session Caching**: Redis for fast session access
2. **Message Queuing**: Asynchronous message processing
3. **Rate Limiting**: Respect API limits and implement backoff
4. **Connection Pooling**: Efficient HTTP client management

## Related Decisions
- ADR-001: GraphQL API Architecture
- ADR-003: Redis for Caching and Sessions
- ADR-005: Payment Integration Strategy
